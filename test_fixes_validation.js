#!/usr/bin/env node

/**
 * Test Suite to Validate the Two Critical Fixes:
 * 1. Comment content is now included in API responses
 * 2. Teachers only get groups they have access to (no more 403 errors)
 */

import fetch from 'node-fetch';

const CONFIG = {
    SAPS_API_URL: 'https://ivslcxob6c.execute-api.us-east-2.amazonaws.com/dev',
    CDS_API_URL: 'https://rxq8k76df4.execute-api.us-east-2.amazonaws.com/dev',
    AUTH_TOKEN: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4YjYxNTAyYmY3N2M5ZTc2MmYzMmRhNiIsInJvbGUiOiJ0ZWFjaGVyIiwiaWF0IjoxNzU3NzgyMTU5LCJleHAiOjE3NTc5NTQ5NTl9.8rMwJzn1X4uynj_H4baP_LpHLUjWt0DqfiWqSKMsQxE',
    TEST_USER_ID: '68b61502bf77c9e762f32da6'
};

class FixValidationTests {
    constructor() {
        this.results = [];
        this.createdCommentId = null;
    }

    log(test, status, message, data = null) {
        const result = { test, status, message, data, timestamp: new Date().toISOString() };
        this.results.push(result);
        
        const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
        console.log(`${emoji} ${test}: ${message}`);
        if (data && process.env.DEBUG) {
            console.log('   Data:', JSON.stringify(data, null, 2));
        }
    }

    /**
     * Test 1: Create a doubt comment to test with
     */
    async testCreateDoubtComment() {
        console.log('\n🔧 Test 1: Creating test doubt comment...');
        
        try {
            const response = await fetch(`${CONFIG.SAPS_API_URL}/video/comment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': CONFIG.AUTH_TOKEN
                },
                body: JSON.stringify({
                    videoId: 171,
                    content: `🧪 TEST COMMENT: Validating fixes at ${new Date().toISOString()}`,
                    isDoubt: true,
                    groupId: "68c761e677213046ce24e2b0",
                    userId: CONFIG.TEST_USER_ID
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.createdCommentId = result.id;
                this.log('Create Test Comment', 'PASS', `Comment created with ID: ${result.id}`, result);
                return result.id;
            } else {
                const error = await response.text();
                this.log('Create Test Comment', 'FAIL', `HTTP ${response.status}: ${error}`);
                return null;
            }
        } catch (error) {
            this.log('Create Test Comment', 'FAIL', error.message);
            return null;
        }
    }

    /**
     * Test 2: Validate that teacher groups endpoint only returns authorized groups
     */
    async testTeacherGroupsAuthorization() {
        console.log('\n🔐 Test 2: Testing teacher groups authorization...');
        
        try {
            const response = await fetch(`${CONFIG.CDS_API_URL}/group/teacher/groups?teacherId=current&isWithoutPagination=true`, {
                headers: {
                    'Authorization': CONFIG.AUTH_TOKEN
                }
            });

            if (response.ok) {
                const result = await response.json();
                const groupCount = result.groups ? result.groups.length : 0;
                
                this.log('Teacher Groups Authorization', 'PASS', 
                    `Retrieved ${groupCount} authorized groups (no unauthorized groups returned)`, 
                    { groupCount, sampleGroups: result.groups?.slice(0, 3) });
                
                return result.groups || [];
            } else {
                const error = await response.text();
                this.log('Teacher Groups Authorization', 'FAIL', `HTTP ${response.status}: ${error}`);
                return [];
            }
        } catch (error) {
            this.log('Teacher Groups Authorization', 'FAIL', error.message);
            return [];
        }
    }

    /**
     * Test 3: Test individual group access (should only work for authorized groups)
     */
    async testIndividualGroupAccess(groups) {
        console.log('\n🎯 Test 3: Testing individual group access...');
        
        if (!groups || groups.length === 0) {
            this.log('Individual Group Access', 'SKIP', 'No groups to test');
            return;
        }

        let successCount = 0;
        let failCount = 0;

        for (const group of groups.slice(0, 3)) { // Test first 3 groups
            try {
                const response = await fetch(
                    `${CONFIG.SAPS_API_URL}/video/comments/group/${group.id || group._id}?isDoubt=true&limit=5`,
                    {
                        headers: {
                            'Authorization': CONFIG.AUTH_TOKEN
                        }
                    }
                );

                if (response.ok) {
                    const result = await response.json();
                    successCount++;
                    console.log(`   ✅ Group ${group.name}: ${result.comments?.length || 0} doubts found`);
                } else if (response.status === 403) {
                    failCount++;
                    console.log(`   ❌ Group ${group.name}: 403 Forbidden (should not happen with fix)`);
                } else {
                    console.log(`   ⚠️  Group ${group.name}: HTTP ${response.status} (${response.statusText})`);
                }
            } catch (error) {
                failCount++;
                console.log(`   ❌ Group ${group.name}: ${error.message}`);
            }
        }

        if (failCount === 0) {
            this.log('Individual Group Access', 'PASS', 
                `All ${successCount} tested groups accessible (no 403 errors)`);
        } else {
            this.log('Individual Group Access', 'FAIL', 
                `${failCount} groups returned 403 errors out of ${successCount + failCount} tested`);
        }
    }

    /**
     * Test 4: Validate comment content is included in responses
     */
    async testCommentContentInclusion() {
        console.log('\n📝 Test 4: Testing comment content inclusion...');
        
        if (!this.createdCommentId) {
            this.log('Comment Content Inclusion', 'SKIP', 'No test comment available');
            return;
        }

        try {
            // Test SAPS comment info endpoint
            const response = await fetch(
                `${CONFIG.SAPS_API_URL}/video/comment/${this.createdCommentId}/info`,
                {
                    headers: {
                        'Authorization': CONFIG.AUTH_TOKEN
                    }
                }
            );

            if (response.ok) {
                const result = await response.json();
                
                if (result.content && result.content.trim().length > 0) {
                    this.log('Comment Content Inclusion', 'PASS', 
                        `Comment content properly included: "${result.content.substring(0, 50)}..."`, 
                        { hasContent: true, contentLength: result.content.length });
                } else {
                    this.log('Comment Content Inclusion', 'FAIL', 
                        'Comment content is missing or empty', result);
                }
            } else {
                const error = await response.text();
                this.log('Comment Content Inclusion', 'FAIL', `HTTP ${response.status}: ${error}`);
            }
        } catch (error) {
            this.log('Comment Content Inclusion', 'FAIL', error.message);
        }
    }

    /**
     * Test 5: Test batch endpoint (if available)
     */
    async testBatchEndpoint(groups) {
        console.log('\n🚀 Test 5: Testing batch endpoint...');
        
        if (!groups || groups.length === 0) {
            this.log('Batch Endpoint', 'SKIP', 'No groups to test');
            return;
        }

        const groupIds = groups.slice(0, 3).map(g => g.id || g._id);

        try {
            const response = await fetch(`${CONFIG.SAPS_API_URL}/video/comments/groups/batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': CONFIG.AUTH_TOKEN
                },
                body: JSON.stringify({
                    groupIds,
                    isDoubt: true,
                    limit: 10
                })
            });

            if (response.ok) {
                const result = await response.json();
                const successfulGroups = result.groupResults?.filter(g => g.success).length || 0;
                const failedGroups = result.groupResults?.filter(g => !g.success).length || 0;
                
                this.log('Batch Endpoint', 'PASS', 
                    `Batch request successful: ${successfulGroups} successful, ${failedGroups} failed groups`, 
                    { totalComments: result.comments?.length || 0, groupResults: result.groupResults });
            } else {
                const error = await response.text();
                this.log('Batch Endpoint', 'FAIL', `HTTP ${response.status}: ${error}`);
            }
        } catch (error) {
            this.log('Batch Endpoint', 'FAIL', error.message);
        }
    }

    /**
     * Generate final report
     */
    generateReport() {
        console.log('\n' + '='.repeat(80));
        console.log('📊 FIX VALIDATION REPORT');
        console.log('='.repeat(80));
        
        const passed = this.results.filter(r => r.status === 'PASS').length;
        const failed = this.results.filter(r => r.status === 'FAIL').length;
        const skipped = this.results.filter(r => r.status === 'SKIP').length;
        
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`⚠️  Skipped: ${skipped}`);
        console.log(`📊 Total: ${this.results.length}`);
        
        console.log('\n📋 DETAILED RESULTS:');
        this.results.forEach((result, index) => {
            const emoji = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
            console.log(`${index + 1}. ${emoji} ${result.test}: ${result.message}`);
        });
        
        console.log('\n🎯 SUMMARY:');
        if (failed === 0) {
            console.log('🎉 All tests passed! Both fixes are working correctly.');
            console.log('   ✅ Comment content is properly included in responses');
            console.log('   ✅ Teachers only receive groups they have access to');
        } else {
            console.log('⚠️  Some tests failed. Review the issues above.');
        }
        
        return { passed, failed, skipped, total: this.results.length };
    }

    /**
     * Run all validation tests
     */
    async runAllTests() {
        console.log('🚀 Starting Fix Validation Tests');
        console.log('='.repeat(80));
        console.log('Testing fixes for:');
        console.log('1. Missing comment content in API responses');
        console.log('2. Unauthorized group access (403 errors)');
        console.log('='.repeat(80));
        
        // Test 1: Create test comment
        await this.testCreateDoubtComment();
        
        // Test 2: Check teacher groups authorization
        const groups = await this.testTeacherGroupsAuthorization();
        
        // Test 3: Test individual group access
        await this.testIndividualGroupAccess(groups);
        
        // Test 4: Validate comment content inclusion
        await this.testCommentContentInclusion();
        
        // Test 5: Test batch endpoint
        await this.testBatchEndpoint(groups);
        
        return this.generateReport();
    }
}

// Run tests if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const validator = new FixValidationTests();
    validator.runAllTests().catch(console.error);
}

export default FixValidationTests;
