"use client"

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Check, X, Loader2, AlertCircle } from 'lucide-react';
import { useFlashcards } from './hooks/useFlashcards';
import { Deck, Flashcard } from './utils/dataMapping';
import { CreateDeckWithAI } from './_components/CreateDeckWithAI';
import { CreateFlashcardModal } from './_components/CreateFlashcardModal';
import { DeckTabView } from './_components/DeckTabView';
import { FlashcardStatistics } from '../../../services/CDS/interfaces';
import { FlashcardsTabView } from './_components/FlashcardsTabView';


export default function Flashcards() {
    const { decks, subjects, years, loading, error, submitAnswers, refreshDecks } = useFlashcards();
    const [deckAtual, setDeckAtual] = useState<Deck | null>(null);
    const [cardAtual, setCardAtual] = useState(0);
    const [mostrandoVerso, setMostrandoVerso] = useState(false);
    const [conhecidos, setConhecidos] = useState<number[]>([]);
    const [revisao, setRevisao] = useState<number[]>([]);
    const [deckCompleto, setDeckCompleto] = useState(false);
    const [escolhendoDeck, setEscolhendoDeck] = useState(true);
    const [answersToSubmit, setAnswersToSubmit] = useState<{ flashcardId: string; isCorrect: boolean }[]>([]);
    const [showCreateFlashcard, setShowCreateFlashcard] = useState(false);
    const [activeTab, setActiveTab] = useState<'decks' | 'flashcards'>('decks');
    const [submittingAnswers, setSubmittingAnswers] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);
    const [originalDeckData, setOriginalDeckData] = useState<any>(null);
    const [cardStatistics, setCardStatistics] = useState<{ [cardApiId: string]: FlashcardStatistics }>({});


    // Note: Removed automatic deck selection to allow StartingDeckMessage to show
    // Users must explicitly select a deck to start studying

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: { staggerChildren: 0.1 }
        }
    };

    const handleVirarCard = () => {
        if (!deckCompleto) {
            setMostrandoVerso(!mostrandoVerso);
        }
    };

    const handleNextCard = async (updatedAnswers?: { flashcardId: string; isCorrect: boolean }[]) => {
        if (!deckAtual) return;
        
        setMostrandoVerso(false);
        
        // Use the updated answers if provided, otherwise use current state
        const currentAnswers = updatedAnswers || answersToSubmit;
        
        // Verificar se o deck está completo após processar o card atual
        const cardsProcessados = new Set();
        // Adicionar IDs únicos ao conjunto
        conhecidos.forEach(id => cardsProcessados.add(id));
        revisao.forEach(id => cardsProcessados.add(id));
        
        // Verificar se todos os cards foram processados
        // FIXED: Use strict equality to ensure all cards are processed
        console.log('Cards processados:', cardsProcessados.size, 'Total cards:', deckAtual.cards.length);
        console.log('Answers to submit:', currentAnswers.length);
        
        if (cardsProcessados.size === deckAtual.cards.length) {
            setDeckCompleto(true);
            // Submit all answers when deck is complete
            if (currentAnswers.length > 0) {
                setSubmittingAnswers(true);
                setSubmitError(null);
                
                try {
                    await submitAnswers(currentAnswers);
                    console.log('Successfully submitted answers:', currentAnswers);
                    setAnswersToSubmit([]);
                } catch (error) {
                    console.error('Error submitting answers:', error);
                    setSubmitError('Erro ao salvar respostas. Tente novamente.');
                } finally {
                    setSubmittingAnswers(false);
                }
            }
            return;
        }
        
        // Se não estamos no último card, avançar para o próximo
        if (cardAtual < deckAtual.cards.length - 1) {
            setCardAtual(cardAtual + 1);
            return;
        }
        
        // Se chegamos ao final do deck mas ainda há cards não processados,
        // voltar para o primeiro card não processado
        const todosIds = deckAtual.cards.map(card => card.id);
        
        // Encontrar cards não processados
        const naoProcessados = todosIds.filter(id => !cardsProcessados.has(id));
        
        if (naoProcessados.length > 0) {
            // Encontrar o índice do primeiro card não processado
            const primeiroNaoProcessadoId = naoProcessados[0];
            const primeiroNaoProcessadoIndex = deckAtual.cards.findIndex(card => card.id === primeiroNaoProcessadoId);
            
            // Voltar para esse card apenas se não for o card atual
            if (primeiroNaoProcessadoIndex !== cardAtual) {
                setCardAtual(primeiroNaoProcessadoIndex);
            } else {
                // Se o único card não processado for o atual, verificar se já foi mostrado
                if (mostrandoVerso) {
                    // Se já foi mostrado, marcar como completo
                    setDeckCompleto(true);
                    // Submit all answers when deck is complete - FIXED: Add proper async handling
                    if (currentAnswers.length > 0) {
                        setSubmittingAnswers(true);
                        setSubmitError(null);
                        
                        try {
                            await submitAnswers(currentAnswers);
                            console.log('Successfully submitted answers:', currentAnswers);
                            setAnswersToSubmit([]);
                        } catch (error) {
                            console.error('Error submitting answers:', error);
                            setSubmitError('Erro ao salvar respostas. Tente novamente.');
                        } finally {
                            setSubmittingAnswers(false);
                        }
                    }
                }
            }
        } else {
            // Se todos foram processados, marcar como completo
            setDeckCompleto(true);
            // Submit all answers when deck is complete
            if (currentAnswers.length > 0) {
                setSubmittingAnswers(true);
                setSubmitError(null);
                
                try {
                    await submitAnswers(currentAnswers);
                    console.log('Successfully submitted answers:', currentAnswers);
                    setAnswersToSubmit([]);
                } catch (error) {
                    console.error('Error submitting answers:', error);
                    setSubmitError('Erro ao salvar respostas. Tente novamente.');
                } finally {
                    setSubmittingAnswers(false);
                }
            }
        }
    };

    const handleAcertei = async () => {
        if (mostrandoVerso && deckAtual) {
            const cardId = deckAtual.cards[cardAtual].id;
            // Verificar se o card já está na lista de revisão e removê-lo se estiver
            if (revisao.includes(cardId)) {
                setRevisao(prev => prev.filter(id => id !== cardId));
            }
            // Adicionar à lista de conhecidos se ainda não estiver lá
            if (!conhecidos.includes(cardId)) {
                setConhecidos(prev => [...prev, cardId]);
            }
            
            // Track answer for API submission
            const currentCard = deckAtual.cards[cardAtual];
            console.log('Adding correct answer for card:', currentCard.apiId, 'at index:', cardAtual);
            const newAnswers = [...answersToSubmit, { 
                flashcardId: currentCard.apiId,
                isCorrect: true 
            }];
            console.log('Updated answers to submit:', newAnswers);
            
            // Update state and pass the new answers to handleNextCard
            setAnswersToSubmit(newAnswers);
            await handleNextCard(newAnswers);
        }
    };

    const handleErrei = async () => {
        if (mostrandoVerso && deckAtual) {
            const cardId = deckAtual.cards[cardAtual].id;
            // Verificar se o card já está na lista de conhecidos e removê-lo se estiver
            if (conhecidos.includes(cardId)) {
                setConhecidos(prev => prev.filter(id => id !== cardId));
            }
            // Adicionar à lista de revisão se ainda não estiver lá
            if (!revisao.includes(cardId)) {
                setRevisao(prev => [...prev, cardId]);
            }
            
            // Track answer for API submission
            const currentCard = deckAtual.cards[cardAtual];
            console.log('Adding incorrect answer for card:', currentCard.apiId, 'at index:', cardAtual);
            const newAnswers = [...answersToSubmit, { 
                flashcardId: currentCard.apiId,
                isCorrect: false 
            }];
            console.log('Updated answers to submit:', newAnswers);
            
            // Update state and pass the new answers to handleNextCard
            setAnswersToSubmit(newAnswers);
            await handleNextCard(newAnswers);
        }
    };

    const handleChangeDeck = (deck: Deck) => {
        setDeckAtual(deck);
        setCardAtual(0);
        setMostrandoVerso(false);
        setConhecidos([]);
        setRevisao([]);
        setDeckCompleto(false);
        setEscolhendoDeck(false);
        setAnswersToSubmit([]);
        setSubmittingAnswers(false);
        setSubmitError(null);
    };

    const handleDeckDataFetched = (originalData: any, statistics: { [cardApiId: string]: FlashcardStatistics }) => {
        setOriginalDeckData(originalData);
        setCardStatistics(statistics);
    };

    const reiniciarDeck = () => {
        setCardAtual(0);
        setMostrandoVerso(false);
        setConhecidos([]);
        setRevisao([]);
        setDeckCompleto(false);
        setAnswersToSubmit([]);
        setSubmittingAnswers(false);
        setSubmitError(null);
    };

    const handleNewDeck = async (newDeck: Deck) => {
        // Note: In a real implementation, this would add the deck to the API
        // For now, we'll just set it as the current deck and refresh the decks list
        setDeckAtual(newDeck);
        setCardAtual(0);
        setMostrandoVerso(false);
        setConhecidos([]);
        setRevisao([]);
        setDeckCompleto(false);
        setEscolhendoDeck(false);
        setAnswersToSubmit([]);
        setSubmittingAnswers(false);
        setSubmitError(null);
        
        // Refresh the decks list to show the new deck
        await refreshDecks();
    };

    const handleFlashcardCreated = () => {
        // Refresh the decks list to get updated flashcard count
        refreshDecks();
        // Close the create flashcard modal
        setShowCreateFlashcard(false);
    };

    // Componente para mensagem de seleção de deck
    const EscolherDeck = () => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center justify-center space-y-6 text-center h-[400px]"
        >
            <motion.div
                className="w-20 h-20 rounded-full bg-blue-500/20 flex items-center justify-center"
                animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, -10, 10, -10, 0]
                }}
                transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                }}
            >
                <BookOpen className="w-10 h-10 text-blue-400" />
            </motion.div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                Escolha um Novo Deck
            </h2>
            <p className="text-blue-300 max-w-md">
                Selecione um deck na lista ao lado para começar seus estudos. 
                Você pode filtrar por ano e matéria para encontrar o conteúdo ideal.
            </p>
        </motion.div>
    );

    // Componente para quando não há decks disponíveis
    const NoDecksAvailable = () => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center justify-center space-y-6 text-center h-[400px]"
        >
            <motion.div
                className="w-20 h-20 rounded-full bg-purple-500/20 flex items-center justify-center"
                animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, -10, 10, -10, 0]
                }}
                transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                }}
            >
                <BookOpen className="w-10 h-10 text-purple-400" />
            </motion.div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
                Nenhum Deck Encontrado
            </h2>
            <p className="text-blue-300 max-w-md">
                Você ainda não possui nenhum deck de flashcards. 
                Crie seu primeiro deck para começar a estudar!
            </p>
            <motion.button
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-200"
                onClick={() => {
                    // Scroll to the CreateDeckWithAI component in the header
                    const headerElement = document.querySelector('header');
                    if (headerElement) {
                        headerElement.scrollIntoView({ behavior: 'smooth' });
                    }
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
            >
                Criar Primeiro Deck
            </motion.button>
        </motion.div>
    );

    // Loading state
    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100 py-8 px-4 sm:px-6 lg:px-8">
                <div className="max-w-[1400px] mx-auto flex items-center justify-center h-[400px]">
                    <motion.div
                        className="flex flex-col items-center space-y-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                    >
                        <Loader2 className="w-8 h-8 text-blue-400 animate-spin" />
                        <p className="text-blue-300">Carregando flashcards...</p>
                    </motion.div>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100 py-8 px-4 sm:px-6 lg:px-8">
                <div className="max-w-[1400px] mx-auto flex items-center justify-center h-[400px]">
                    <motion.div
                        className="flex flex-col items-center space-y-4 text-center"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                    >
                        <AlertCircle className="w-12 h-12 text-red-400" />
                        <h2 className="text-xl font-bold text-red-400">Erro ao carregar flashcards</h2>
                        <p className="text-blue-300 max-w-md">{error}</p>
                        <motion.button
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                            onClick={() => refreshDecks()}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            Tentar novamente
                        </motion.button>
                    </motion.div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100 py-8 px-4 sm:px-6 lg:px-8">
            <motion.div 
                className="max-w-[1400px] mx-auto"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
            >
                <div className="flex justify-between items-center mb-8">
                    <motion.h1
                        className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500"
                        initial={{ y: -20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                    >
                        Flashcards
                    </motion.h1>
                    {activeTab === 'decks' && <CreateDeckWithAI onDeckCreated={handleNewDeck} />}
                </div>

                {/* Tab Navigation */}
                <motion.div 
                    className="flex space-x-1 mb-8 bg-blue-900/20 p-1 rounded-lg border border-blue-500/30"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                >
                    <motion.button
                        className={`flex-1 px-6 py-3 rounded-md font-semibold transition-all duration-200 ${
                            activeTab === 'decks'
                                ? 'bg-blue-600 text-white shadow-lg'
                                : 'text-blue-300 hover:text-white hover:bg-blue-800/30'
                        }`}
                        onClick={() => setActiveTab('decks')}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        <div className="flex items-center justify-center gap-2">
                            <BookOpen className="w-4 h-4" />
                            Decks
                        </div>
                    </motion.button>
                    <motion.button
                        className={`flex-1 px-6 py-3 rounded-md font-semibold transition-all duration-200 ${
                            activeTab === 'flashcards'
                                ? 'bg-green-600 text-white shadow-lg'
                                : 'text-green-300 hover:text-white hover:bg-green-800/30'
                        }`}
                        onClick={() => setActiveTab('flashcards')}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        <div className="flex items-center justify-center gap-2">
                            <BookOpen className="w-4 h-4" />
                            Flashcards
                        </div>
                    </motion.button>
                </motion.div>

                {/* Tab Content */}
                {activeTab === 'decks' ? (
                    <DeckTabView
                        decks={decks}
                        deckAtual={deckAtual}
                        cardAtual={cardAtual}
                        mostrandoVerso={mostrandoVerso}
                        conhecidos={conhecidos}
                        revisao={revisao}
                        deckCompleto={deckCompleto}
                        escolhendoDeck={escolhendoDeck}
                        originalDeckData={originalDeckData}
                        cardStatistics={cardStatistics}
                        onHandleChangeDeck={handleChangeDeck}
                        onHandleVirarCard={handleVirarCard}
                        onHandleAcertei={handleAcertei}
                        onHandleErrei={handleErrei}
                        onReiniciarDeck={reiniciarDeck}
                        onSetEscolhendoDeck={setEscolhendoDeck}
                        onDeckDataFetched={handleDeckDataFetched}
                        submittingAnswers={submittingAnswers}
                        submitError={submitError}
                    />
                ) : (
                    <FlashcardsTabView
                        decks={decks}
                        deckAtual={deckAtual}
                        onSetDeckAtual={setDeckAtual}
                        onSetShowCreateFlashcard={setShowCreateFlashcard}
                        onSetActiveTab={setActiveTab}
                        onFlashcardsCreated={refreshDecks}
                    />
                )}
            </motion.div>

            {/* Estilos necessários para o efeito 3D */}
            <style jsx global>{`
                .perspective-1000 {
                    perspective: 1000px;
                }
                .preserve-3d {
                    transform-style: preserve-3d;
                }
                .backface-hidden {
                    backface-visibility: hidden;
                }
                .rotate-y-180 {
                    transform: rotateY(180deg);
                }
                .custom-scrollbar::-webkit-scrollbar {
                    width: 6px;
                }
                .custom-scrollbar::-webkit-scrollbar-track {
                    background: rgba(30, 58, 138, 0.1);
                    border-radius: 10px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb {
                    background: rgba(79, 70, 229, 0.5);
                    border-radius: 10px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                    background: rgba(79, 70, 229, 0.7);
                }
            `}</style>

            {/* Create Flashcard Modal */}
            {showCreateFlashcard && (
                <CreateFlashcardModal
                    onFlashcardCreated={handleFlashcardCreated}
                    onClose={() => setShowCreateFlashcard(false)}
                />
            )}
        </div>
    );
}
