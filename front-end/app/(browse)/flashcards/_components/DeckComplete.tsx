import { motion } from 'framer-motion';
import { Check, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { Deck } from '../utils/dataMapping';
import { FlashcardStatistics } from '../../../../services/CDS/interfaces';

interface DeckCompleteProps {
    conhecidos: number[];
    revisao: number[];
    deckData?: Deck;
    cardStatistics?: { [cardApiId: string]: FlashcardStatistics };
    onReiniciar: () => void;
    onEscolherOutroDeck: () => void;
    submittingAnswers?: boolean;
    submitError?: string | null;
}

export function DeckComplete({ 
    conhecidos, 
    revisao, 
    deckData,
    cardStatistics,
    onReiniciar, 
    onEscolherOutroDeck, 
    submittingAnswers = false, 
    submitError = null 
}: DeckCompleteProps) {
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex flex-col items-center justify-center space-y-6 text-center"
        >
            {submittingAnswers ? (
                <>
                    <motion.div
                        className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center"
                        initial={{ rotate: 0 }}
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                        <Loader2 className="w-10 h-10 text-white" />
                    </motion.div>
                    <h2 className="text-2xl font-bold">Salvando Respostas...</h2>
                    <div className="text-blue-300">
                        <p>Enviando suas respostas para o servidor.</p>
                        <p className="mt-2">Aguarde um momento...</p>
                    </div>
                </>
            ) : submitError ? (
                <>
                    <motion.div
                        className="w-20 h-20 rounded-full bg-red-500 flex items-center justify-center"
                        initial={{ scale: 0.8 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3 }}
                    >
                        <AlertCircle className="w-10 h-10 text-white" />
                    </motion.div>
                    <h2 className="text-2xl font-bold text-red-400">Erro ao Salvar</h2>
                    <div className="text-red-300">
                        <p>{submitError}</p>
                        <p className="mt-2">Suas respostas foram registradas localmente.</p>
                    </div>
                    <div className="flex gap-4">
                        <motion.button
                            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={onReiniciar}
                        >
                            Reiniciar Deck
                        </motion.button>
                        <motion.button
                            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={onEscolherOutroDeck}
                        >
                            Escolher Outro Deck
                        </motion.button>
                    </div>
                </>
            ) : (
                <>
                    <motion.div
                        className="w-20 h-20 rounded-full bg-green-500 flex items-center justify-center"
                        initial={{ rotate: 0 }}
                        animate={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                    >
                        <Check className="w-10 h-10 text-white" />
                    </motion.div>
                    <h2 className="text-2xl font-bold">Deck Concluído!</h2>
                    <div className="text-blue-300">
                        <p>Você completou todos os flashcards deste deck.</p>
                        <p className="mt-2">
                            {conhecidos.length} cards conhecidos • {revisao.length} cards para revisão
                        </p>
                    </div>
                    
                    {/* Statistics Display */}
                    {deckData && cardStatistics && Object.keys(cardStatistics).length > 0 && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="w-full max-w-2xl bg-gray-800/50 rounded-lg p-6 border border-gray-700"
                        >
                            <h3 className="text-lg font-semibold text-white mb-4">Estatísticas dos Cards</h3>
                            <div className="grid gap-3">
                                {deckData.cards && deckData.cards.length > 0 ? deckData.cards.map((card, index) => {
                                    // Safety check for card data
                                    if (!card || !card.frente) {
                                        console.warn('Card data is missing or incomplete:', card);
                                        return null;
                                    }
                                    
                                    // Get statistics for this card
                                    const cardStats = cardStatistics?.[card.apiId];
                                    const accuracy = cardStats ? 
                                        (cardStats.numberOfTimesAnswered > 0 ? 
                                            (cardStats.numberOfCorrectAnswers / cardStats.numberOfTimesAnswered) * 100 : 0) : 0;
                                    
                                    return (
                                        <motion.div
                                            key={card.id}
                                            initial={{ opacity: 0, x: -20 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            transition={{ delay: 0.4 + index * 0.1 }}
                                            className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg border border-gray-600"
                                        >
                                            <div className="flex items-center space-x-3">
                                                <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-sm font-medium">
                                                    {index + 1}
                                                </div>
                                                <div className="text-left">
                                                    <p className="text-sm text-gray-300 truncate max-w-xs">
                                                        {card.frente.length > 50 ? `${card.frente.substring(0, 50)}...` : card.frente}
                                                    </p>
                                                    <p className="text-xs text-gray-400">
                                                        {card.dificuldade}
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <div className="flex items-center space-x-4">
                                                {cardStats && cardStats.numberOfTimesAnswered > 0 ? (
                                                    <>
                                                        <div className="text-right">
                                                            <div className="flex items-center space-x-1">
                                                                {accuracy >= 70 ? (
                                                                    <TrendingUp className="w-4 h-4 text-green-400" />
                                                                ) : accuracy >= 40 ? (
                                                                    <Minus className="w-4 h-4 text-yellow-400" />
                                                                ) : (
                                                                    <TrendingDown className="w-4 h-4 text-red-400" />
                                                                )}
                                                                <span className={`text-sm font-medium ${
                                                                    accuracy >= 70 ? 'text-green-400' : 
                                                                    accuracy >= 40 ? 'text-yellow-400' : 'text-red-400'
                                                                }`}>
                                                                    {accuracy.toFixed(0)}%
                                                                </span>
                                                            </div>
                                                            <p className="text-xs text-gray-400">
                                                                {cardStats.numberOfCorrectAnswers}/{cardStats.numberOfTimesAnswered} acertos
                                                            </p>
                                                        </div>
                                                    </>
                                                ) : (
                                                    <div className="text-right">
                                                        <p className="text-sm text-gray-500">Não respondido</p>
                                                        <p className="text-xs text-gray-500">0/0 acertos</p>
                                                    </div>
                                                )}
                                            </div>
                                        </motion.div>
                                    );
                                }) : (
                                    <div className="text-center py-4">
                                        <p className="text-gray-400">Nenhum card encontrado neste deck.</p>
                                    </div>
                                )}
                            </div>
                        </motion.div>
                    )}
                    
                    {/* No Statistics Message */}
                    {deckData && (!cardStatistics || Object.keys(cardStatistics).length === 0) && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="w-full max-w-2xl bg-gray-800/30 rounded-lg p-4 border border-gray-700"
                        >
                            <p className="text-gray-400 text-sm">
                                Estatísticas dos cards serão exibidas após você responder algumas perguntas.
                            </p>
                        </motion.div>
                    )}
                    <div className="flex gap-4">
                        <motion.button
                            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={onReiniciar}
                        >
                            Reiniciar Deck
                        </motion.button>
                        <motion.button
                            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={onEscolherOutroDeck}
                        >
                            Escolher Outro Deck
                        </motion.button>
                    </div>
                </>
            )}
        </motion.div>
    );
} 