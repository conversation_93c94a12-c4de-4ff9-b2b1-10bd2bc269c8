import { motion } from 'framer-motion';
import { Check, X } from 'lucide-react';

interface FlashCardProps {
    card: {
        frente: string;
        verso: string;
    };
    mostrandoVerso: boolean;
    onVirar: () => void;
    onAcertei: () => void;
    onErrei: () => void;
}

export function FlashCard({ card, mostrandoVerso, onVirar, onAcertei, onErrei }: FlashCardProps) {
    console.log('FlashCard received card:', card);
    console.log('Card frente:', card?.frente);
    console.log('Card verso:', card?.verso);
    
    return (
        <>
            {/* Card com perspectiva melhorada */}
            <div className="relative w-full max-w-2xl mx-auto aspect-[3/2] perspective-1000">
                <motion.div
                    className="absolute inset-0 rounded-xl preserve-3d"
                    animate={{ rotateY: mostrandoVerso ? 180 : 0 }}
                    transition={{ duration: 0.6 }}
                >
                    {/* Frente do Card */}
                    <div className="absolute inset-0 backface-hidden bg-blue-900/30 rounded-xl backdrop-blur-lg 
                        border border-blue-500/30 p-8 flex flex-col items-center justify-center">
                        <div className="text-sm text-blue-300 mb-4">Pergunta:</div>
                        <div className="text-2xl font-medium text-center">
                            {card?.frente || 'Pergunta não disponível'}
                        </div>
                    </div>

                    {/* Verso do Card */}
                    <div className="absolute inset-0 backface-hidden bg-purple-900/30 rounded-xl backdrop-blur-lg 
                        border border-purple-500/30 p-8 flex flex-col items-center justify-center rotate-y-180">
                        <div className="text-sm text-purple-300 mb-4">Resposta:</div>
                        <div className="text-2xl font-medium text-center">
                            {card?.verso || 'Resposta não disponível'}
                        </div>
                    </div>
                </motion.div>
            </div>

            {/* Botões e Controles */}
            <div className="flex flex-col items-center gap-6">
                <motion.button
                    className="px-8 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-lg"
                    onClick={onVirar}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                >
                    {mostrandoVerso ? "Virar para Pergunta" : "Virar para Resposta"}
                </motion.button>

                {mostrandoVerso && (
                    <motion.div 
                        className="flex items-center gap-6"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <motion.button
                            className="px-8 py-4 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center gap-3 text-lg"
                            onClick={onErrei}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <X className="w-6 h-6" />
                            Errei
                        </motion.button>

                        <motion.button
                            className="px-8 py-4 bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center gap-3 text-lg"
                            onClick={onAcertei}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Check className="w-6 h-6" />
                            Acertei
                        </motion.button>
                    </motion.div>
                )}
            </div>
        </>
    );
} 