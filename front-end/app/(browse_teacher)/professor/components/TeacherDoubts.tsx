'use client'

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Clock,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  MessageSquare,
  User,
  PlayCircle,
  Filter,
  Zap,
  Star
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useUserInformation } from '@/context/UserContext';
import TeacherManagerController from '@/services/CDS/api/TeacherManagerController';
import DoubtsController, { IDoubtComment } from '@/services/SAPS/api/DoubtsController';
import { ITeacherGroup } from '@/services/CDS/interfaces/teacherManager';
import { Skeleton } from '@/components/ui/skeleton';

interface TeacherDoubtsProps {
  // Props opcionais para personalização se necessário
}

export const TeacherDoubts: React.FC<TeacherDoubtsProps> = () => {
  const router = useRouter();
  const { authData } = useAuth();
  const { user } = useUserInformation();
  const [doubts, setDoubts] = useState<IDoubtComment[]>([]);
  const [groups, setGroups] = useState<ITeacherGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'answered'>('all');

  useEffect(() => {
    const fetchDoubtsData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Busca os grupos do professor
        const groupsResponse = await TeacherManagerController.getTeacherGroups('current', true, 1, 100, true);
        setGroups(groupsResponse.groups);

        // Extrai os IDs dos grupos
        const groupIds = groupsResponse.groups.map((group: ITeacherGroup) => group.id);

        if (groupIds.length > 0) {
          const startTime = performance.now();

          const doubtsData = await DoubtsController.getDoubtsFromMultipleGroupsBatch(groupIds, {
            limit: 100
          });

          const endTime = performance.now();
          console.log(`[TeacherDoubts] Batch request completed in ${Math.round(endTime - startTime)}ms, found ${doubtsData.length} doubts`);

          setDoubts(doubtsData);
        }
      } catch (err: any) {
        console.error('Erro ao buscar dúvidas:', err);
        setError('Erro ao carregar dúvidas. Tente novamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchDoubtsData();
  }, []);

  const getStatusInfo = (status: number) => {
    switch (status) {
      case -1:
        return { label: 'Pendente', color: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30', icon: Clock };
      case 0:
        return { label: 'Em andamento', color: 'bg-blue-500/20 text-blue-300 border-blue-500/30', icon: AlertCircle };
      case 1:
        return { label: 'Respondida', color: 'bg-green-500/20 text-green-300 border-green-500/30', icon: CheckCircle };
      default:
        return { label: 'Desconhecido', color: 'bg-gray-500/20 text-gray-300 border-gray-500/30', icon: Clock };
    }
  };

  const filteredDoubts = doubts.filter(doubt => {
    const matchesSearch = (doubt.content || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doubt.username || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doubt.video?.title || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'pending' && doubt.doubtStatus === -1) ||
      (statusFilter === 'answered' && doubt.doubtStatus === 1);

    return matchesSearch && matchesStatus;
  });

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
            Dúvidas dos Alunos
          </h2>
          <p className="text-blue-400 text-sm mt-1">
            Gerencie e responda às dúvidas dos seus estudantes
          </p>
        </div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Badge variant="secondary" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 px-4 py-2">
            <HelpCircle className="h-4 w-4 mr-1" />
            {filteredDoubts.length} dúvidas
          </Badge>
        </motion.div>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card className="bg-gradient-to-r from-[#0A1340]/80 to-[#1A237E]/80 border-blue-700/50 p-6 backdrop-blur-sm">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-blue-400" />
                <Input
                  placeholder="Buscar por conteúdo, autor ou vídeo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-[#061037]/80 border-blue-600/50 text-blue-100 placeholder-blue-400 h-11 focus:border-blue-400"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant={statusFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('all')}
                  size="sm"
                  className={`${statusFilter === 'all'
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 border-0'
                    : 'border-blue-600/50 text-blue-300 hover:bg-blue-600/20'
                    }`}
                >
                  <Filter className="h-3 w-3 mr-1" />
                  Todas
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant={statusFilter === 'pending' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('pending')}
                  size="sm"
                  className={`${statusFilter === 'pending'
                    ? 'bg-gradient-to-r from-yellow-600 to-yellow-700 border-0'
                    : 'border-yellow-600/50 text-yellow-300 hover:bg-yellow-600/20'
                    }`}
                >
                  <Clock className="h-3 w-3 mr-1" />
                  Pendentes
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant={statusFilter === 'answered' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('answered')}
                  size="sm"
                  className={`${statusFilter === 'answered'
                    ? 'bg-gradient-to-r from-green-600 to-green-700 border-0'
                    : 'border-green-600/50 text-green-300 hover:bg-green-600/20'
                    }`}
                >
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Respondidas
                </Button>
              </motion.div>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {loading ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            {[...Array(5)].map((_, i) => (
              <Card key={i} className="bg-gradient-to-r from-[#0A1340]/60 to-[#1A237E]/60 border-blue-700/30 p-6 backdrop-blur-sm">
                <div className="space-y-4">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-32 bg-blue-600/30" />
                      <Skeleton className="h-6 w-20 bg-blue-600/30" />
                    </div>
                    <Skeleton className="h-8 w-24 bg-blue-600/30" />
                  </div>
                  <Skeleton className="h-16 w-full bg-blue-600/30" />
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-4 w-24 bg-blue-600/30" />
                    <Skeleton className="h-4 w-32 bg-blue-600/30" />
                  </div>
                </div>
              </Card>
            ))}
          </motion.div>
        ) : error ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card className="bg-gradient-to-r from-red-900/30 to-red-800/30 border-red-600/50 p-8 text-center backdrop-blur-sm">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-red-300">Erro ao carregar</h3>
              <p className="text-red-400 mb-4">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 border-0"
              >
                <Zap className="h-4 w-4 mr-2" />
                Tentar novamente
              </Button>
            </Card>
          </motion.div>
        ) : filteredDoubts.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card className="bg-gradient-to-r from-[#0A1340]/60 to-[#1A237E]/60 border-blue-700/30 p-12 text-center backdrop-blur-sm">
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <HelpCircle className="h-16 w-16 text-blue-400 mx-auto mb-4" />
              </motion.div>
              <h3 className="text-2xl font-semibold mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
                Nenhuma dúvida encontrada
              </h3>
              <p className="text-blue-400">
                {searchTerm || statusFilter !== 'all'
                  ? 'Tente ajustar os filtros de busca.'
                  : 'Não há dúvidas registradas no momento.'}
              </p>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            {filteredDoubts.map((doubt, index) => {
              const statusInfo = getStatusInfo(doubt.doubtStatus);
              const StatusIcon = statusInfo.icon;

              return (
                <motion.div
                  key={doubt.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="group"
                >
                  <Card className="bg-gradient-to-r from-[#0A1340]/80 to-[#1A237E]/80 border-blue-700/50 p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20">
                    <div className="space-y-4">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2 text-blue-300">
                            <User className="h-4 w-4" />
                            <span className="font-medium">{doubt.username}</span>
                          </div>
                          {doubt.video && (
                            <div className="flex items-center gap-2 text-purple-300">
                              <PlayCircle className="h-4 w-4" />
                              <span className="text-sm">{doubt.video.title}</span>
                            </div>
                          )}
                        </div>
                        <motion.div whileHover={{ scale: 1.1 }}>
                          <Badge className={`${statusInfo.color} border flex items-center gap-1`}>
                            <StatusIcon className="h-3 w-3" />
                            {statusInfo.label}
                          </Badge>
                        </motion.div>
                      </div>

                      <div className="bg-[#061037]/50 rounded-lg p-4 border border-blue-800/30">
                        <div className="flex items-start gap-3">
                          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-full">
                            <MessageSquare className="h-4 w-4 text-white" />
                          </div>
                          <div className="flex-1">
                            <p className="text-blue-100 leading-relaxed">
                              {doubt.content}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-between items-center text-sm">
                        <div className="flex items-center gap-2 text-blue-400">
                          <Clock className="h-3 w-3" />
                          <span>{formatDateTime(doubt.createdAt)}</span>
                        </div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-blue-300 hover:text-blue-100 hover:bg-blue-600/20"
                            onClick={() => {
                              if (doubt.video?.id) {
                                router.push(`/aulas/${doubt.video.id}?comment=${doubt.id}`);
                              }
                            }}
                          >
                            <Star className="h-3 w-3 mr-1" />
                            Visualizar
                          </Button>
                        </motion.div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
