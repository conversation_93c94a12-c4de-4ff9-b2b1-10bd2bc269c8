import CdsAPI_Calling from './ApiCaller';
import { CreateF<PERSON>cardRequest, FlashcardAnswer, CreateDeckRequest, UpdateDeckRequest, DeleteDecksRequest, Deck, UpdateFlashcardRequest, DeleteFlashcardsRequest } from '../interfaces';

export default class FlashcardController {
    // Criar flashcard manual
    static async createFlashcard(request: CreateFlashcardRequest) {
        return await CdsAPI_Calling.apiCall('/flashcard', {
            method: 'POST',
            data: request
        });
    }

    // Pegar seus próprios flashcards com paginação
    static async getSelfFlashcards(page: number = 1, limit: number = 5) {
        return await CdsAPI_Calling.apiCall(`/flashcard/self`, {
            method: 'GET',
            params: { page, limit }
        });
    }

    // Pegar os próprios decks com paginação
    static async getSelfDecks(page: number = 0, limit: number = 5) {
        return await CdsAPI_Calling.apiCall(`/flashcard/decks`, {
            method: 'GET',
            params: { page, limit }
        });
    }

    // Pegar deck por ID
    static async getDeckById(deckId: string) {
        return await CdsAPI_Calling.apiCall(`/flashcard/deck/${deckId}`, {
            method: 'GET'
        });
    }

    // Enviar respostas de flashcards em lote
    static async batchAnswerFlashcards(answers: FlashcardAnswer[]) {
        return await CdsAPI_Calling.apiCall('/flashcard/batch-answer', {
            method: 'POST',
            data: { answers }
        });
    }

    // Criar um novo deck
    static async createDeck(request: CreateDeckRequest) : Promise<Deck> {
        return await CdsAPI_Calling.apiCall('/flashcard/deck', {
            method: 'POST',
            data: request
        });
    }

    // Atualizar um deck (adicionar/remover flashcards)
    static async updateDeck(request: UpdateDeckRequest) {
        return await CdsAPI_Calling.apiCall('/flashcard/deck', {
            method: 'PATCH',
            data: request
        });
    }

    static async updateFlashcard(request: UpdateFlashcardRequest) {
        return await CdsAPI_Calling.apiCall('/flashcard', {
            method: 'PATCH',
            data: request
        });
    }

    static async deleteFlashcards(request: DeleteFlashcardsRequest) {
        return await CdsAPI_Calling.apiCall('/flashcard', {
            method: 'DELETE',
            data: request
        });
    }

    // Deletar decks por IDs
    static async deleteDecks(request: DeleteDecksRequest) {
        return await CdsAPI_Calling.apiCall('/flashcard/decks', {
            method: 'DELETE',
            data: request
        });
    }
}