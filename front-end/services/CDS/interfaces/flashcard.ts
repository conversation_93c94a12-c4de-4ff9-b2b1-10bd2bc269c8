export interface Flashcard {
  id?: string;
  question: string;
  answer: string;
  tags?: string[];
  difficulty: 1 | 2 | 3;
  olympiadIds?: string[];
  statistics?: FlashcardStatistics;
  ownerId: string;
  isFromAi?: boolean;
}

export interface FlashcardStatistics {
  flashcardId: string | null;
  ownerId: string | null;
  userId: string | null;
  numberOfCorrectAnswers: number;
  numberOfIncorrectAnswers: number;
  numberOfTimesAnswered: number;
}

export interface FlashcardAnswer {
  flashcardId: string;
  isCorrect: boolean;
  userId: string;
}

export interface CreateFlashcardRequest {
  flashCardData: {
    question: string;
    answer: string;
    tags?: string[];
    difficulty: 1 | 2 | 3;
    ownerId: string;
  };
  isFromAi: boolean;
  numberOfFlashcards: number;
}

export interface DeckFlashcard {
  flashcard: Flashcard;
  sortOrder: number;
}

export interface Deck {
  id?: string;
  name: string;
  description: string;
  flashcards: DeckFlashcard[];
  statistics: {
    numberOfTimesAnswered: number | null;
  } | null;
  ownerId: string;
  shareLink?: string;
}

export interface CreateDeckRequest {
  name: string;
  description: string;
  ownerId: string;
}

export interface UpdateDeckRequest {
  updateData: Partial<Deck>;
  flashCardsIdsToAdd: string[];
  flashCardsIdsToRemove: string[];
  deckId: string;
}

export interface updateDeck_ModifyFlashcardsRequest extends UpdateDeckRequest {
  flashCardsIdsToAdd: string[];
  flashCardsIdsToRemove: string[];
  deckId: string;
  updateData: {};
}

export interface DeleteDecksRequest {
  deckIds: string[];
} 

export interface UpdateFlashcardRequest {
  updateData: Partial<Flashcard>;
  flashcardId: string;
}

export interface DeleteFlashcardsRequest {
  flashcardIds: string[];
}