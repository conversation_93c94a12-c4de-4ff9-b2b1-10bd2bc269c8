#!/usr/bin/env node

/**
 * Comprehensive test suite for the doubt system fixes and optimizations
 * Tests both the bug fixes and performance improvements
 */

import fetch from 'node-fetch';

const CONFIG = {
    SAPS_API_URL: process.env.SAPS_API_URL || 'https://ivslcxob6c.execute-api.us-east-2.amazonaws.com/dev',
    CDS_API_URL: process.env.CDS_API_URL || 'https://rxq8k76df4.execute-api.us-east-2.amazonaws.com/dev',
    AUTH_TOKEN: process.env.AUTH_TOKEN || 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4YjYxNTAyYmY3N2M5ZTc2MmYzMmRhNiIsInJvbGUiOiJ0ZWFjaGVyIiwiaWF0IjoxNzU3NzgyMTU5LCJleHAiOjE3NTc5NTQ5NTl9.8rMwJzn1X4uynj_H4baP_LpHLUjWt0DqfiWqSKMsQxE',
    
    // Test data
    TEST_STUDENT: {
        id: '68b61502bf77c9e762f32da6',
        name: 'Comprehensive Test Student',
        token: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4YjYxNTAyYmY3N2M5ZTc2MmYzMmRhNiIsInJvbGUiOiJ0ZWFjaGVyIiwiaWF0IjoxNzU3NzgyMTU5LCJleHAiOjE3NTc5NTQ5NTl9.8rMwJzn1X4uynj_H4baP_LpHLUjWt0DqfiWqSKMsQxE'
    },
    TEST_TEACHER: {
        id: '68b61502bf77c9e762f32da6',
        name: 'Comprehensive Test Teacher',
        token: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI2IjY4YjYxNTAyYmY3N2M5ZTc2MmYzMmRhNiIsInJvbGUiOiJ0ZWFjaGVyIiwiaWF0IjoxNzU3NzgyMTU5LCJleHAiOjE3NTc5NTQ5NTl9.8rMwJzn1X4uynj_H4baP_LpHLUjWt0DqfiWqSKMsQxE'
    },
    TEST_VIDEO: {
        id: 171,
        title: 'Comprehensive Test Video'
    },
    TEST_GROUPS: [
        { id: 'test-group-001', name: 'Test Group 1' },
        { id: 'test-group-002', name: 'Test Group 2' },
        { id: 'test-group-003', name: 'Test Group 3' },
        { id: 'test-group-004', name: 'Test Group 4' },
        { id: 'test-group-005', name: 'Test Group 5' }
    ]
};

/**
 * Comprehensive test suite class
 */
class ComprehensiveTestSuite {
    constructor() {
        this.results = [];
        this.createdComments = [];
        this.startTime = Date.now();
    }

    /**
     * Log test result
     */
    logResult(testName, success, message, data = null, duration = null) {
        const result = {
            test: testName,
            success,
            message,
            data,
            duration,
            timestamp: new Date().toISOString()
        };
        this.results.push(result);
        
        const status = success ? '✅' : '❌';
        const durationStr = duration ? ` (${duration}ms)` : '';
        console.log(`${status} ${testName}: ${message}${durationStr}`);
    }

    /**
     * Test 1: Bug Fix Validation - Comment Creation with CDS Sync
     */
    async testBugFixCommentCreation() {
        console.log('\n🔧 Testing Bug Fix: Comment Creation with CDS Sync');
        
        const testStartTime = Date.now();
        
        try {
            const commentData = {
                videoId: CONFIG.TEST_VIDEO.id,
                userId: CONFIG.TEST_STUDENT.id,
                username: CONFIG.TEST_STUDENT.name,
                content: `Bug fix test doubt - ${new Date().toISOString()}`,
                isDoubt: true
            };

            console.log('Creating doubt comment...');
            const response = await fetch(`${CONFIG.SAPS_API_URL}/video/comment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': CONFIG.TEST_STUDENT.token
                },
                body: JSON.stringify(commentData)
            });

            const duration = Date.now() - testStartTime;

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.id) {
                this.createdComments.push(result.id);
                this.logResult(
                    'Bug Fix - Comment Creation',
                    true,
                    `Comment created successfully with ID: ${result.id}`,
                    result,
                    duration
                );
                return result.id;
            } else {
                this.logResult(
                    'Bug Fix - Comment Creation',
                    false,
                    'No comment ID returned',
                    result,
                    duration
                );
                return null;
            }
        } catch (error) {
            const duration = Date.now() - testStartTime;
            this.logResult(
                'Bug Fix - Comment Creation',
                false,
                `Error: ${error.message}`,
                null,
                duration
            );
            return null;
        }
    }

    /**
     * Test 2: Performance Test - Individual vs Batch Requests
     */
    async testPerformanceComparison() {
        console.log('\n⚡ Testing Performance: Individual vs Batch Requests');
        
        const groupIds = CONFIG.TEST_GROUPS.map(g => g.id);
        
        // Test individual requests (legacy method)
        console.log('Testing individual requests...');
        const individualStartTime = Date.now();
        
        try {
            const individualResults = await Promise.all(
                groupIds.map(async (groupId) => {
                    const response = await fetch(
                        `${CONFIG.SAPS_API_URL}/video/comments/group/${groupId}?isDoubt=true&limit=10`,
                        {
                            headers: { 'Authorization': CONFIG.TEST_TEACHER.token }
                        }
                    );
                    return response.ok ? await response.json() : { comments: [] };
                })
            );
            
            const individualDuration = Date.now() - individualStartTime;
            const individualComments = individualResults.reduce((acc, result) => 
                acc + (result.comments?.length || 0), 0
            );
            
            this.logResult(
                'Performance - Individual Requests',
                true,
                `${groupIds.length} requests completed, ${individualComments} comments found`,
                { requestCount: groupIds.length, commentsFound: individualComments },
                individualDuration
            );
            
            // Test batch request (optimized method)
            console.log('Testing batch request...');
            const batchStartTime = Date.now();
            
            const batchResponse = await fetch(
                `${CONFIG.SAPS_API_URL}/video/comments/groups/batch`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': CONFIG.TEST_TEACHER.token
                    },
                    body: JSON.stringify({
                        groupIds,
                        isDoubt: true,
                        limit: 10
                    })
                }
            );
            
            const batchDuration = Date.now() - batchStartTime;
            
            if (batchResponse.ok) {
                const batchResult = await batchResponse.json();
                const batchComments = batchResult.comments?.length || 0;
                
                this.logResult(
                    'Performance - Batch Request',
                    true,
                    `1 batch request completed, ${batchComments} comments found`,
                    { requestCount: 1, commentsFound: batchComments },
                    batchDuration
                );
                
                // Performance comparison
                const improvement = ((individualDuration - batchDuration) / individualDuration * 100).toFixed(1);
                this.logResult(
                    'Performance - Comparison',
                    batchDuration < individualDuration,
                    `Batch request ${improvement}% faster (${individualDuration}ms vs ${batchDuration}ms)`,
                    { 
                        individualDuration, 
                        batchDuration, 
                        improvement: `${improvement}%`,
                        requestReduction: `${groupIds.length}:1`
                    }
                );
                
            } else {
                this.logResult(
                    'Performance - Batch Request',
                    false,
                    `Batch request failed: HTTP ${batchResponse.status}`,
                    null,
                    batchDuration
                );
            }
            
        } catch (error) {
            this.logResult(
                'Performance - Comparison',
                false,
                `Error during performance test: ${error.message}`
            );
        }
    }

    /**
     * Test 3: End-to-End Doubt Visibility
     */
    async testEndToEndDoubtVisibility() {
        console.log('\n🔄 Testing End-to-End: Doubt Visibility');
        
        // Wait for async processing
        console.log('⏳ Waiting 10 seconds for async processing...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        const testStartTime = Date.now();
        
        try {
            // Test teacher panel retrieval
            const response = await fetch(
                `${CONFIG.SAPS_API_URL}/video/comments/groups/batch`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': CONFIG.TEST_TEACHER.token
                    },
                    body: JSON.stringify({
                        groupIds: CONFIG.TEST_GROUPS.map(g => g.id),
                        isDoubt: true,
                        limit: 50
                    })
                }
            );
            
            const duration = Date.now() - testStartTime;
            
            if (response.ok) {
                const result = await response.json();
                const doubts = result.comments || [];
                
                // Check if our created comments appear
                const createdDoubtsFound = doubts.filter(doubt => 
                    this.createdComments.includes(doubt.id?.toString())
                ).length;
                
                this.logResult(
                    'End-to-End - Doubt Visibility',
                    createdDoubtsFound > 0,
                    `Found ${doubts.length} total doubts, ${createdDoubtsFound} from this test session`,
                    { 
                        totalDoubts: doubts.length, 
                        createdDoubtsFound,
                        createdComments: this.createdComments.length
                    },
                    duration
                );
                
            } else {
                this.logResult(
                    'End-to-End - Doubt Visibility',
                    false,
                    `Teacher panel request failed: HTTP ${response.status}`,
                    null,
                    duration
                );
            }
            
        } catch (error) {
            const duration = Date.now() - testStartTime;
            this.logResult(
                'End-to-End - Doubt Visibility',
                false,
                `Error: ${error.message}`,
                null,
                duration
            );
        }
    }

    /**
     * Test 4: Error Handling and Resilience
     */
    async testErrorHandling() {
        console.log('\n🛡️  Testing Error Handling and Resilience');
        
        // Test invalid group IDs
        const testStartTime = Date.now();
        
        try {
            const response = await fetch(
                `${CONFIG.SAPS_API_URL}/video/comments/groups/batch`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': CONFIG.TEST_TEACHER.token
                    },
                    body: JSON.stringify({
                        groupIds: ['invalid-group-1', 'invalid-group-2'],
                        isDoubt: true,
                        limit: 10
                    })
                }
            );
            
            const duration = Date.now() - testStartTime;
            
            if (response.ok) {
                const result = await response.json();
                const failedGroups = result.groupResults?.filter(g => !g.success).length || 0;
                
                this.logResult(
                    'Error Handling - Invalid Groups',
                    failedGroups > 0,
                    `Gracefully handled ${failedGroups} invalid groups`,
                    result.groupResults,
                    duration
                );
            } else {
                this.logResult(
                    'Error Handling - Invalid Groups',
                    false,
                    `Unexpected error response: HTTP ${response.status}`,
                    null,
                    duration
                );
            }
            
        } catch (error) {
            const duration = Date.now() - testStartTime;
            this.logResult(
                'Error Handling - Invalid Groups',
                false,
                `Error: ${error.message}`,
                null,
                duration
            );
        }
    }

    /**
     * Generate comprehensive test report
     */
    generateReport() {
        const totalDuration = Date.now() - this.startTime;
        
        console.log('\n' + '='.repeat(80));
        console.log('📊 COMPREHENSIVE TEST REPORT');
        console.log('='.repeat(80));
        
        const totalTests = this.results.length;
        const passedTests = this.results.filter(r => r.success === true).length;
        const failedTests = this.results.filter(r => r.success === false).length;
        
        console.log(`\n📈 Summary:`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   ✅ Passed: ${passedTests}`);
        console.log(`   ❌ Failed: ${failedTests}`);
        console.log(`   ⏱️  Total Duration: ${Math.round(totalDuration / 1000)}s`);
        
        console.log(`\n📋 Detailed Results:`);
        this.results.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            const duration = result.duration ? ` (${result.duration}ms)` : '';
            console.log(`${index + 1}. ${status} ${result.test}: ${result.message}${duration}`);
        });
        
        console.log(`\n🎯 Key Findings:`);
        
        // Bug fix validation
        const bugFixTests = this.results.filter(r => r.test.includes('Bug Fix'));
        const bugFixSuccess = bugFixTests.every(r => r.success);
        console.log(`   Bug Fixes: ${bugFixSuccess ? '✅ Working' : '❌ Issues detected'}`);
        
        // Performance improvements
        const perfTests = this.results.filter(r => r.test.includes('Performance'));
        const perfSuccess = perfTests.some(r => r.success && r.test.includes('Comparison'));
        console.log(`   Performance: ${perfSuccess ? '✅ Improved' : '❌ No improvement'}`);
        
        // End-to-end functionality
        const e2eTests = this.results.filter(r => r.test.includes('End-to-End'));
        const e2eSuccess = e2eTests.every(r => r.success);
        console.log(`   End-to-End: ${e2eSuccess ? '✅ Working' : '❌ Issues detected'}`);
        
        console.log(`\n📝 Recommendations:`);
        if (failedTests === 0) {
            console.log('   🎉 All tests passed! The solution is ready for deployment.');
        } else {
            console.log('   🔧 Some tests failed. Review the detailed results above.');
            console.log('   📞 Consider running individual debug scripts for failed components.');
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            duration: totalDuration,
            results: this.results
        };
    }

    /**
     * Run all comprehensive tests
     */
    async runAllTests() {
        console.log('🚀 Starting Comprehensive Test Suite');
        console.log('='.repeat(80));
        
        console.log('📋 Test Configuration:');
        console.log(`   SAPS API: ${CONFIG.SAPS_API_URL}`);
        console.log(`   CDS API: ${CONFIG.CDS_API_URL}`);
        console.log(`   Test Groups: ${CONFIG.TEST_GROUPS.length}`);
        console.log(`   Test Video: ${CONFIG.TEST_VIDEO.id}`);
        
        // Run all test suites
        await this.testBugFixCommentCreation();
        await this.testPerformanceComparison();
        await this.testEndToEndDoubtVisibility();
        await this.testErrorHandling();
        
        return this.generateReport();
    }
}

// Run tests if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const testSuite = new ComprehensiveTestSuite();
    testSuite.runAllTests().catch(console.error);
}

export default ComprehensiveTestSuite;
