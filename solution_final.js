// Resumo da solução final para getCommentsByGroupId com enriquecimento de dados

/*
IMPLEMENTAÇÃO FINAL:

1. getCommentsByGroupId agora busca dados completos do SAPS para cada comentário
2. Para cada comentário/dúvida retornado, faz chamada para SAPS getCommentInfo
3. <PERSON><PERSON> o objeto comment com:
   - content: Texto da dúvida/comentário
   - doubtText: Alias para content (para clareza)
   - videoSubject: Matéria do vídeo
   - videoTitle: Título do vídeo
   - commentCreatedAt: Data de criação do comentário

RESPOSTA ESPERADA:
{
  "comments": [
    {
      "commentId": "43",
      "userId": "68b61502bf77c9e762f32da6",
      "videoId": 234,
      "playlistId": 15,
      "subject": "", // Continua vazio no LessonMetric
      "user": { ... },
      "isDoubt": true,
      "doubtStatus": -1,
      "createdAt": "...",
      "updatedAt": "...",
      // DADOS ENRIQUECIDOS DO SAPS:
      "content": "mano se nao for fodeu...", // TEXTO DA DÚVIDA
      "doubtText": "mano se nao for fodeu...", // ALIAS
      "videoSubject": "Matemática", // SUBJECT DO VÍDEO
      "videoTitle": "Equações do 2º grau",
      "commentCreatedAt": "2025-09-13T19:56:20.000Z"
    }
  ],
  "pagination": { ... },
  "groupInfo": { ... }
}

BENEFÍCIOS:
✅ Teacher vê o texto completo da dúvida do aluno
✅ Teacher vê a matéria do vídeo onde foi feita a dúvida
✅ Dados sempre atualizados (vem direto do SAPS)
✅ LessonMetric não armazena dados que pertencem ao SAPS
✅ Arquitetura limpa com separação de responsabilidades
*/

console.log('Solução implementada: getCommentsByGroupId com enriquecimento completo!');
console.log('Agora retorna tanto o conteúdo da dúvida quanto o subject do vídeo!');