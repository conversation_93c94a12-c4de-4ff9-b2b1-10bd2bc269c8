{"Stacks": [{"StackId": "arn:aws:cloudformation:us-east-2:010526255193:stack/saps-service-dev/3dc95130-f441-11ef-a501-0284ff250101", "StackName": "saps-service-dev", "ChangeSetId": "arn:aws:cloudformation:us-east-2:010526255193:changeSet/saps-service-dev-change-set/da745ab4-1b10-4518-b61e-39d8a82cafcf", "Description": "The AWS CloudFormation template for this Serverless application", "CreationTime": "2025-02-26T12:57:31.897000+00:00", "LastUpdatedTime": "2025-09-15T00:48:19.125000+00:00", "RollbackConfiguration": {}, "StackStatus": "UPDATE_COMPLETE", "DisableRollback": false, "NotificationARNs": [], "Capabilities": ["CAPABILITY_IAM", "CAPABILITY_NAMED_IAM"], "Outputs": [{"OutputKey": "GetPlaylistsByFilterLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylistsByFilter:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistsByFilterLambdaFunctionQualifiedArn"}, {"OutputKey": "GetPlaylistByTypeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylistByType:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistByTypeLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSavedVideosLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetSavedVideos:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetSavedVideosLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentInfoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetCommentInfo:3", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetCommentInfoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideosLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideos:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideosLambdaFunctionQualifiedArn"}, {"OutputKey": "RemoveVideoSaveLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-RemoveVideoSave:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-RemoveVideoSaveLambdaFunctionQualifiedArn"}, {"OutputKey": "RemoveVideoLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-RemoveVideoLike:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-RemoveVideoLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "ServerlessDeploymentBucketName", "OutputValue": "saps-service-dev-serverlessdeploymentbucket-svuntvyv8m1v", "ExportName": "sls-saps-service-dev-ServerlessDeploymentBucketName"}, {"OutputKey": "GetLikedVideosLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetLikedVideos:30", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetLikedVideosLambdaFunctionQualifiedArn"}, {"OutputKey": "GetPlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylist:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideosBySubjectLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideosBySubject:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideosBySubjectLambdaFunctionQualifiedArn"}, {"OutputKey": "AddWatchedToVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddWatchedToVideo:24", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddWatchedToVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSinglePlaylistByFilterLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetSinglePlaylistByFilter:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetSinglePlaylistByFilterLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-DeleteVideo:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-DeleteVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentsByGroupLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetCommentsByGroup:18", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetCommentsByGroupLambdaFunctionQualifiedArn"}, {"OutputKey": "GetPlaylistByOwnerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylistByOwner:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistByOwnerLambdaFunctionQualifiedArn"}, {"OutputKey": "AddLikeToVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddLikeToVideo:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddLikeToVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "AddCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddComment:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-DeleteComment:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-DeleteCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideoWithStatsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideoWithStats:30", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideoWithStatsLambdaFunctionQualifiedArn"}, {"OutputKey": "AddSaveToVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddSaveToVideo:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddSaveToVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentsByGroupsBatchLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetCommentsByGroupsBatch:2", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetCommentsByGroupsBatchLambdaFunctionQualifiedArn"}, {"OutputKey": "DeletePlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-DeletePlaylist:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-DeletePlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateTeacherLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-CreateTeacher:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-CreateTeacherLambdaFunctionQualifiedArn"}, {"OutputKey": "RemoveCommentLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-RemoveCommentLike:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-RemoveCommentLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "CreatePlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-CreatePlaylist:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-CreatePlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "AuthorizerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-authorizer:28", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AuthorizerLambdaFunctionQualifiedArn"}, {"OutputKey": "AddVideosToPlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddVideosToPlaylist:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddVideosToPlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdatePlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-UpdatePlaylist:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-UpdatePlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-CreateVideo:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-CreateVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "AddLikeToCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddLikeToComment:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddLikeToCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideoByIdLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideoById:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideoByIdLambdaFunctionQualifiedArn"}, {"OutputKey": "ServiceEndpoint", "OutputValue": "https://ivslcxob6c.execute-api.us-east-2.amazonaws.com/dev", "Description": "URL of the service endpoint", "ExportName": "sls-saps-service-dev-ServiceEndpoint"}], "Tags": [{"Key": "STAGE", "Value": "dev"}], "EnableTerminationProtection": false, "DriftInformation": {"StackDriftStatus": "NOT_CHECKED"}}]}