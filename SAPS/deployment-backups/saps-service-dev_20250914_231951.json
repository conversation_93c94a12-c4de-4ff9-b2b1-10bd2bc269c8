{"Stacks": [{"StackId": "arn:aws:cloudformation:us-east-2:010526255193:stack/saps-service-dev/3dc95130-f441-11ef-a501-0284ff250101", "StackName": "saps-service-dev", "ChangeSetId": "arn:aws:cloudformation:us-east-2:010526255193:changeSet/saps-service-dev-change-set/ad2cdb43-d739-421c-8e9f-e4c28bd8b744", "Description": "The AWS CloudFormation template for this Serverless application", "CreationTime": "2025-02-26T12:57:31.897000+00:00", "LastUpdatedTime": "2025-09-15T02:17:44.287000+00:00", "RollbackConfiguration": {}, "StackStatus": "UPDATE_COMPLETE", "DisableRollback": false, "NotificationARNs": [], "Capabilities": ["CAPABILITY_IAM", "CAPABILITY_NAMED_IAM"], "Outputs": [{"OutputKey": "GetPlaylistsByFilterLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylistsByFilter:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistsByFilterLambdaFunctionQualifiedArn"}, {"OutputKey": "GetPlaylistByTypeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylistByType:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistByTypeLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSavedVideosLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetSavedVideos:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetSavedVideosLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentInfoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetCommentInfo:4", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetCommentInfoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideosLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideos:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideosLambdaFunctionQualifiedArn"}, {"OutputKey": "RemoveVideoSaveLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-RemoveVideoSave:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-RemoveVideoSaveLambdaFunctionQualifiedArn"}, {"OutputKey": "RemoveVideoLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-RemoveVideoLike:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-RemoveVideoLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "ServerlessDeploymentBucketName", "OutputValue": "saps-service-dev-serverlessdeploymentbucket-svuntvyv8m1v", "ExportName": "sls-saps-service-dev-ServerlessDeploymentBucketName"}, {"OutputKey": "GetLikedVideosLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetLikedVideos:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetLikedVideosLambdaFunctionQualifiedArn"}, {"OutputKey": "GetPlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylist:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideosBySubjectLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideosBySubject:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideosBySubjectLambdaFunctionQualifiedArn"}, {"OutputKey": "AddWatchedToVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddWatchedToVideo:25", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddWatchedToVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSinglePlaylistByFilterLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetSinglePlaylistByFilter:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetSinglePlaylistByFilterLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-DeleteVideo:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-DeleteVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentsByGroupLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetCommentsByGroup:19", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetCommentsByGroupLambdaFunctionQualifiedArn"}, {"OutputKey": "GetPlaylistByOwnerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetPlaylistByOwner:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetPlaylistByOwnerLambdaFunctionQualifiedArn"}, {"OutputKey": "AddLikeToVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddLikeToVideo:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddLikeToVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "AddCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddComment:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-DeleteComment:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-DeleteCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideoWithStatsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideoWithStats:31", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideoWithStatsLambdaFunctionQualifiedArn"}, {"OutputKey": "AddSaveToVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddSaveToVideo:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddSaveToVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentsByGroupsBatchLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetCommentsByGroupsBatch:3", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetCommentsByGroupsBatchLambdaFunctionQualifiedArn"}, {"OutputKey": "DeletePlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-DeletePlaylist:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-DeletePlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateTeacherLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-CreateTeacher:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-CreateTeacherLambdaFunctionQualifiedArn"}, {"OutputKey": "RemoveCommentLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-RemoveCommentLike:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-RemoveCommentLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "CreatePlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-CreatePlaylist:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-CreatePlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "AuthorizerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-authorizer:29", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AuthorizerLambdaFunctionQualifiedArn"}, {"OutputKey": "AddVideosToPlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddVideosToPlaylist:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddVideosToPlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdatePlaylistLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-UpdatePlaylist:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-UpdatePlaylistLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateVideoLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-CreateVideo:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-CreateVideoLambdaFunctionQualifiedArn"}, {"OutputKey": "AddLikeToCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-AddLikeToComment:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-AddLikeToCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "GetVideoByIdLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:saps-service-dev-GetVideoById:32", "Description": "Current Lambda function version", "ExportName": "sls-saps-service-dev-GetVideoByIdLambdaFunctionQualifiedArn"}, {"OutputKey": "ServiceEndpoint", "OutputValue": "https://ivslcxob6c.execute-api.us-east-2.amazonaws.com/dev", "Description": "URL of the service endpoint", "ExportName": "sls-saps-service-dev-ServiceEndpoint"}], "Tags": [{"Key": "STAGE", "Value": "dev"}], "EnableTerminationProtection": false, "DriftInformation": {"StackDriftStatus": "NOT_CHECKED"}}]}