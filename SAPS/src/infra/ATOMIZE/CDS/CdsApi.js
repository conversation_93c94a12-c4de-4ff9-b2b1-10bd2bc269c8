import ApiCaller from "../../ApiHandler/index.js";
import { env } from "../../../env/index.js";
import CompositeValidator, { MissingInputsValidator } from "../../../../utils/validators/index.js";
import { UpstreamServiceError } from "../../../application/customErrors/index.js";

if (!env.CDS_API_URL) {
  throw Error("Missing env: CDS_API_URL");
}
const api = new ApiCaller(env.CDS_API_URL);

export default class CdsApi {
  /**
   * Adds a lesson metric value for a specific user and video.
   *
   * @async
   * @param {Object} params - The parameters for adding a lesson metric value.
   * @param {string} params.id - The ID of the user.
   * @param {string} params.videoId - The ID of the video.
   * @param {string} params.playlistId - The ID of the playlist.
   * @param {string} params.subject - The subject of the lesson.
   * @param {string} params.type - The type of metric being added.
   * @param {string} [params.olympiadId] - The ID of the associated Olympiad (optional).
   * @param {string} [params.videoOwnerId] - The ID of the video's owner (optional).
   * @param {Array} [params.tags] - Tags related to the lesson (optional).
   * @returns {Promise<Object>} - The updated lesson metric object.
   * @throws {Error} - Throws an error if required parameters are missing or if the API call fails.
   */
  static async addLessonMetricValue({
    id,
    videoId,
    playlistId,
    subject,
    type,
    olympiadId,
    videoOwnerId = "ATOMIZE",
    tags = [],
    token,
  }) {
    console.log("Adding new lesson metric value", {
      id,
      videoId,
      playlistId,
      subject,
      type,
      olympiadId,
      videoOwnerId,
      tags,
    });
    const inputValidator = new CompositeValidator(new MissingInputsValidator());
    inputValidator.validate({ id, videoId, subject, tags });

    const body = {
      videoId,
      playlistId,
      subject,
      olympiadId,
      videoOwnerId,
      tags,
    };
    const url = `/user/lessonMetric/${id}`;

    try {
      console.log("Token before parsing:", token);
      const parsedToken = token.split(" ")[1];
      console.log("Parsed token:", parsedToken);
      const updatedLessonMetric = await api.post(url, body, {}, parsedToken);
      return updatedLessonMetric;
    } catch (error) {
      console.log("Error on addLessonMetricValue", error);
      throw new Error(error);
    }
  }

  /**
   * Adds or removes a like from a video for a specific user.
   *
   * @async
   * @param {Object} params - The parameters for adding or removing a like.
   * @param {string} params.id - The ID of the user.
   * @param {string} params.videoId - The ID of the video.
   * @param {string} [params.playlistId] - The ID of the playlist (optional).
   * @returns {Promise<{isLiked:Boolean}>} - The updated video like object.
   * @throws {Error} - Throws an error if the API call fails.
   */
  static async addOrRemoveVideoLike({ id, videoId, playlistId, token }) {
    console.log("TRIGGERING CDS INSIDE SAPS addOrRemoveVideoLike");
    const body = { id, videoId, playlistId };
    const url = `/user/lessonMetric/like`;
    console.log({ body });

    try {
      const parsedToken = token.split(" ")[1];
      const updatedVideoLike = await api.patch(url, body, {}, parsedToken);
      return updatedVideoLike;
    } catch (error) {
      console.log("Error on addOrRemoveVideoLike", error);
      throw new Error(error);
    }
  }

  /**
   * Adds or removes a save from a video for a specific user.
   *
   * @async
   * @param {Object} params - The parameters for adding or removing a save.
   * @param {string} params.id - The ID of the user.
   * @param {string} params.videoId - The ID of the video.
   * @param {string} [params.playlistId] - The ID of the playlist (optional).
   * @returns {Promise<Object>} - The updated video save object.
   * @throws {Error} - Throws an error if the API call fails.
   */
  static async addOrRemoveVideoSave({ id, videoId, playlistId, token }) {
    const body = { id, videoId, playlistId };
    const url = `/user/lessonMetric/save`;
    console.log(
      `Calling ${url} with body : id ${id}, videoId ${videoId},playlistId ${playlistId}`
    );
    try {
      const parsedToken = token.split(" ")[1];
      console.log("parsedToken", parsedToken)
      const updatedVideoSaved = await api.patch(url, body, {}, parsedToken);
      return updatedVideoSaved;
    } catch (error) {
      console.log("Error on addOrRemoveVideoSave", error);
      console.log(error.message, error.code);
      throw new Error(error);
    }
  }

  /**
   * Adds or removes a watched status from a video for a specific user.
   *
   * @async
   * @param {Object} params - The parameters for adding or removing a watched status.
   * @param {string} params.id - The ID of the user.
   * @param {string} params.videoId - The ID of the video.
   * @param {string} [params.playlistId] - The ID of the playlist (optional).
   * @returns {Promise<{isWatched: Boolean}>} - The updated video watched status.
   * @throws {Error} - Throws an error if the API call fails.
   */
  static async addVideoWatched({ id, videoId, playlistId, token }) {
    console.log("TRIGGERING CDS INSIDE SAPS addVideoWatched", {
      id,
      videoId,
      playlistId,
      token,
    });
    const body = { id, videoId, playlistId };
    const url = `/user/lessonMetric/watched`;

    try {
      const parsedToken = token.split(" ")[1];
      const updatedVideoWatched = await api.patch(url, body, {}, parsedToken);
      return updatedVideoWatched;
    } catch (error) {
      console.log("Error on addOrRemoveVideoWatched", error);
      throw new Error(error);
    }
  }

  /**
   * Gets a video with statistics from the CDS.
   *
   * @async
   * @param {Object} params - The parameters for getting a video with statistics.
   * @param {string} params.id - The ID of the user.
   * @param {string} params.videoId - The ID of the video.
   * @param {string} [params.playlistId] - The ID of the playlist (optional).
   * @returns {Promise<Object>} - The video with statistics.
   * @throws {Error} - Throws an error if the API call fails.
   */
  static async getVideoWithStats({ id, videoId, playlistId, token }) {
    const url = `/user/getVideoWithStats/lessonMetric/${id}?videoId=${videoId}&playlistId=${playlistId}`;
    try {
      const parsedToken = token.split(" ")[1];
      const videoWithStats = await api.get(url, {}, parsedToken);
      return videoWithStats;
    } catch (error) {
      return null;
    }
  }

  static async addOrRemoveCommentLikeFromUser({ userId, commentId, token }) {
    try {
      const parsedToken = token.split(" ")[1];
      const url = `/user/commentLikeMetric/${userId}`;
      const response = await api.patch(
        url,
        { key: commentId.toString() },
        {},
        parsedToken
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Adds or removes a comment from a video for a specific user.
   *
   * @async
   * @param {Object} params - The parameters for adding or removing a comment.
   * @param {string} params.userId - The ID of the user.
   * @param {string} params.videoId - The ID of the video.
   * @param {string} params.commentId - The ID of the comment.
   * @param {boolean} params.isDoubt - Whether the comment is a doubt.
   * @param {boolean} params.isRemove - Whether to remove the comment.
   * @param {string} params.token - The authentication token.
   * @returns {Promise<Object>} - The updated comment object.
   * @throws {Error} - Throws an error if the API call fails.
   */
  static async addOrRemoveCommentFromUser({ userId, videoId, commentId, isDoubt, isRemove, playlistId, token }) {
    try {
      if (!token) throw new Error('Missing Authorization token');
      const parsedToken = token.includes(' ') ? token.split(' ')[1] : token;
      const url = `/user/videoCommentMetric/${userId}`;

      console.log(`DEBUG - CDS API call: ${url}`, {
        videoId,
        commentId,
        isDoubt,
        isRemove,
        playlistId,
        userId
      });

      const response = await api.patch(
        url,
        { videoId, commentId, isDoubt, isRemove, playlistId },
        { timeout: 10000 }, // 10 second timeout
        parsedToken
      );

      console.log(`DEBUG - CDS API response:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`ERROR - CDS API call failed:`, {
        url: `/user/videoCommentMetric/${userId}`,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Gets comments by group ID from CDS API.
   *
   * @async
   * @param {Object} params - The parameters for getting comments by group.
   * @param {string} params.groupId - The ID of the group.
   * @param {string} [params.videoId] - The ID of the video to filter by (optional).
   * @param {boolean} [params.isDoubt] - Whether to filter for doubts only (optional).
   * @param {number} [params.page] - Page number for pagination (optional, default: 1).
   * @param {number} [params.limit] - Number of items per page (optional, default: 20).
   * @param {string} params.token - The authentication token.
   * @returns {Promise<Object>} - The comments data with pagination and group info.
   * @throws {Error} - Throws an error if the API call fails.
   */
  static async getCommentsByGroupId({ groupId, videoId, isDoubt, page, limit, token }) {
    const started = Date.now();
    const TIMEOUT_MS = parseInt(env.CDS_TIMEOUT_MS || '5000', 10);
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);
    try {
      const parsedToken = (token || '').split(' ')[1];
      if (!parsedToken) {
        throw new UpstreamServiceError('CDS', 'Missing bearer token for CDS call', 401, 401);
      }
      const url = `/user/comments/group/${groupId}`;

      const queryParams = new URLSearchParams();
      if (videoId) queryParams.append('videoId', videoId);
      if (isDoubt !== undefined) queryParams.append('isDoubt', isDoubt.toString());
      if (page) queryParams.append('page', page.toString());
      if (limit) queryParams.append('limit', limit.toString());
      const fullUrl = queryParams.toString() ? `${url}?${queryParams.toString()}` : url;

      const requestUrl = `${api.baseURL}${fullUrl}`;
      console.log('CDS request (commentsByGroup)', { requestUrl, timeoutMs: TIMEOUT_MS });

      const res = await fetch(requestUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${parsedToken}`
        },
        signal: controller.signal
      });

      const duration = Date.now() - started;
      const contentType = res.headers.get('content-type');
      console.log('CDS response meta', { status: res.status, durationMs: duration, contentType });

      let payloadText = '';
      try { payloadText = await res.text(); } catch { /* ignore */ }
      if (!res.ok) {
        console.log('CDS non-ok body snippet', payloadText.slice(0, 300));
        // Propaga status orig se for 4xx/5xx conhecido
        const mappedStatus = res.status >= 400 && res.status < 600 ? res.status : 502;
        throw new UpstreamServiceError('CDS', `HTTP ${res.status}`, mappedStatus, res.status);
      }
      // Tenta parsear
      let data;
      if (contentType && contentType.includes('application/json')) {
        try { data = payloadText ? JSON.parse(payloadText) : null; } catch { data = payloadText; }
      } else if (payloadText === 'true' || payloadText === 'false') {
        data = payloadText === 'true';
      } else {
        data = payloadText;
      }
      console.log('CDS parsed response summary', { keys: data && typeof data === 'object' ? Object.keys(data).slice(0, 10) : null });
      return data;
    } catch (error) {
      const code = error?.code || error?.cause?.code;
      if (error.name === 'AbortError') {
        throw new UpstreamServiceError('CDS', `Timeout after ${TIMEOUT_MS}ms`, 504);
      }
      if (code === 'ENOTFOUND') {
        throw new UpstreamServiceError('CDS', 'DNS resolution failed (host not found)');
      }
      if (error instanceof UpstreamServiceError) throw error;
      console.log('Error on getCommentsByGroupId raw', { message: error.message, stack: error.stack });
      throw new UpstreamServiceError('CDS', error.message || 'Unknown error');
    } finally {
      clearTimeout(timeoutId);
    }
  }
}
