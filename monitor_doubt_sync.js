#!/usr/bin/env node

/**
 * Monitoring script to track doubt synchronization between SAPS and CDS
 * This helps identify when and why doubts fail to sync
 */

import fetch from 'node-fetch';

const CONFIG = {
    SAPS_API_URL: process.env.SAPS_API_URL || 'https://your-saps-api-url.com',
    CDS_API_URL: process.env.CDS_API_URL || 'https://your-cds-api-url.com',
    MONGODB_CONNECTION: process.env.MONGODB_CONNECTION || 'mongodb://localhost:27017/cds',
    CHECK_INTERVAL: 30000, // 30 seconds
    AUTH_TOKEN: process.env.AUTH_TOKEN || 'Bearer your-token'
};

/**
 * Monitor class for tracking doubt synchronization
 */
class DoubtSyncMonitor {
    constructor() {
        this.isRunning = false;
        this.stats = {
            totalChecks: 0,
            syncFailures: 0,
            orphanedComments: 0,
            lastCheck: null
        };
    }

    /**
     * Check for orphaned comments (exist in SAPS but not in CDS)
     */
    async checkOrphanedComments() {
        console.log('🔍 Checking for orphaned doubt comments...');
        
        try {
            // This would require a custom endpoint or direct database access
            // For now, we'll log what needs to be checked
            
            console.log('📋 Manual check required:');
            console.log('1. Query SAPS database for comments with isDoubt=true');
            console.log('2. For each doubt comment, check if it exists in CDS LessonMetrics');
            console.log('3. Report any comments that exist in SAPS but not in CDS');
            
            // Simulated check - replace with actual implementation
            const orphanedCount = 0; // This would be the actual count
            
            this.stats.orphanedComments = orphanedCount;
            
            if (orphanedCount > 0) {
                console.log(`⚠️  Found ${orphanedCount} orphaned doubt comments`);
            } else {
                console.log('✅ No orphaned doubt comments found');
            }
            
            return orphanedCount;
            
        } catch (error) {
            console.error('❌ Error checking orphaned comments:', error.message);
            return -1;
        }
    }

    /**
     * Test CDS connectivity and response time
     */
    async testCDSConnectivity() {
        console.log('🌐 Testing CDS connectivity...');
        
        const startTime = Date.now();
        
        try {
            // Test a simple CDS endpoint
            const response = await fetch(`${CONFIG.CDS_API_URL}/health`, {
                method: 'GET',
                headers: {
                    'Authorization': CONFIG.AUTH_TOKEN
                },
                timeout: 5000
            });
            
            const responseTime = Date.now() - startTime;
            
            if (response.ok) {
                console.log(`✅ CDS is responsive (${responseTime}ms)`);
                return { healthy: true, responseTime };
            } else {
                console.log(`⚠️  CDS returned ${response.status} (${responseTime}ms)`);
                return { healthy: false, responseTime, status: response.status };
            }
            
        } catch (error) {
            const responseTime = Date.now() - startTime;
            console.log(`❌ CDS connectivity failed (${responseTime}ms): ${error.message}`);
            return { healthy: false, responseTime, error: error.message };
        }
    }

    /**
     * Test SAPS connectivity
     */
    async testSAPSConnectivity() {
        console.log('🌐 Testing SAPS connectivity...');
        
        const startTime = Date.now();
        
        try {
            // Test a simple SAPS endpoint
            const response = await fetch(`${CONFIG.SAPS_API_URL}/health`, {
                method: 'GET',
                headers: {
                    'Authorization': CONFIG.AUTH_TOKEN
                },
                timeout: 5000
            });
            
            const responseTime = Date.now() - startTime;
            
            if (response.ok) {
                console.log(`✅ SAPS is responsive (${responseTime}ms)`);
                return { healthy: true, responseTime };
            } else {
                console.log(`⚠️  SAPS returned ${response.status} (${responseTime}ms)`);
                return { healthy: false, responseTime, status: response.status };
            }
            
        } catch (error) {
            const responseTime = Date.now() - startTime;
            console.log(`❌ SAPS connectivity failed (${responseTime}ms): ${error.message}`);
            return { healthy: false, responseTime, error: error.message };
        }
    }

    /**
     * Check recent doubt creation activity
     */
    async checkRecentActivity() {
        console.log('📊 Checking recent doubt activity...');
        
        try {
            // This would query recent comments with isDoubt=true
            console.log('📋 Manual check required:');
            console.log('1. Query SAPS for comments created in last 24 hours with isDoubt=true');
            console.log('2. For each, verify it exists in CDS LessonMetrics');
            console.log('3. Report sync success rate');
            
            // Simulated data - replace with actual queries
            const recentDoubts = 0;
            const syncedDoubts = 0;
            const syncRate = recentDoubts > 0 ? (syncedDoubts / recentDoubts) * 100 : 100;
            
            console.log(`📈 Recent activity: ${recentDoubts} doubts, ${syncRate.toFixed(1)}% sync rate`);
            
            return { total: recentDoubts, synced: syncedDoubts, rate: syncRate };
            
        } catch (error) {
            console.error('❌ Error checking recent activity:', error.message);
            return null;
        }
    }

    /**
     * Run a single monitoring check
     */
    async runCheck() {
        console.log('\n' + '='.repeat(50));
        console.log(`🔍 Doubt Sync Monitor - ${new Date().toISOString()}`);
        console.log('='.repeat(50));
        
        this.stats.totalChecks++;
        this.stats.lastCheck = new Date();
        
        // Test connectivity
        const sapsHealth = await this.testSAPSConnectivity();
        const cdsHealth = await this.testCDSConnectivity();
        
        // Check for issues
        const orphanedCount = await this.checkOrphanedComments();
        const activity = await this.checkRecentActivity();
        
        // Update stats
        if (orphanedCount > 0) {
            this.stats.syncFailures++;
        }
        
        // Summary
        console.log('\n📊 Check Summary:');
        console.log(`   SAPS Health: ${sapsHealth.healthy ? '✅' : '❌'} (${sapsHealth.responseTime}ms)`);
        console.log(`   CDS Health: ${cdsHealth.healthy ? '✅' : '❌'} (${cdsHealth.responseTime}ms)`);
        console.log(`   Orphaned Comments: ${orphanedCount >= 0 ? orphanedCount : 'Unknown'}`);
        
        if (activity) {
            console.log(`   Recent Activity: ${activity.total} doubts, ${activity.rate.toFixed(1)}% sync rate`);
        }
        
        // Alerts
        if (!sapsHealth.healthy || !cdsHealth.healthy) {
            console.log('\n🚨 ALERT: Service connectivity issues detected!');
        }
        
        if (orphanedCount > 0) {
            console.log('\n🚨 ALERT: Orphaned doubt comments detected!');
            console.log('   Action required: Investigate sync failures and repair data');
        }
        
        if (activity && activity.rate < 90) {
            console.log('\n🚨 ALERT: Low sync success rate detected!');
            console.log('   Action required: Check SAPS → CDS integration');
        }
    }

    /**
     * Start continuous monitoring
     */
    async startMonitoring() {
        if (this.isRunning) {
            console.log('⚠️  Monitor is already running');
            return;
        }
        
        this.isRunning = true;
        console.log('🚀 Starting Doubt Sync Monitor...');
        console.log(`   Check interval: ${CONFIG.CHECK_INTERVAL / 1000} seconds`);
        console.log(`   SAPS API: ${CONFIG.SAPS_API_URL}`);
        console.log(`   CDS API: ${CONFIG.CDS_API_URL}`);
        
        while (this.isRunning) {
            try {
                await this.runCheck();
            } catch (error) {
                console.error('❌ Monitor check failed:', error.message);
            }
            
            if (this.isRunning) {
                console.log(`\n⏳ Next check in ${CONFIG.CHECK_INTERVAL / 1000} seconds...`);
                await new Promise(resolve => setTimeout(resolve, CONFIG.CHECK_INTERVAL));
            }
        }
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        this.isRunning = false;
        console.log('🛑 Stopping Doubt Sync Monitor...');
        
        console.log('\n📊 Final Statistics:');
        console.log(`   Total Checks: ${this.stats.totalChecks}`);
        console.log(`   Sync Failures: ${this.stats.syncFailures}`);
        console.log(`   Last Check: ${this.stats.lastCheck?.toISOString() || 'Never'}`);
    }

    /**
     * Run a single check and exit
     */
    async runOnce() {
        console.log('🔍 Running single doubt sync check...');
        await this.runCheck();
        console.log('\n✅ Single check completed');
    }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
    const monitor = new DoubtSyncMonitor();
    
    const command = process.argv[2] || 'once';
    
    if (command === 'start' || command === 'monitor') {
        // Continuous monitoring
        monitor.startMonitoring().catch(console.error);
        
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Received SIGINT, shutting down...');
            monitor.stopMonitoring();
            process.exit(0);
        });
        
    } else if (command === 'once' || command === 'check') {
        // Single check
        monitor.runOnce().catch(console.error);
        
    } else {
        console.log('Usage: node monitor_doubt_sync.js [once|start]');
        console.log('  once  - Run a single check (default)');
        console.log('  start - Start continuous monitoring');
    }
}

export default DoubtSyncMonitor;
