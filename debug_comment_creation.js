#!/usr/bin/env node

/**
 * Debug script to test the comment creation flow and identify why doubts are not appearing
 * in the teacher panel.
 */

import fetch from 'node-fetch';

// Configuration - Update these with actual values
const CONFIG = {
    SAPS_API_URL: 'https://ivslcxob6c.execute-api.us-east-2.amazonaws.com/dev',
    CDS_API_URL: 'https://rxq8k76df4.execute-api.us-east-2.amazonaws.com/dev',
    TEST_USER_ID: 'test-user-id',
    TEST_VIDEO_ID: 123,
    TEST_GROUP_ID: 'test-group-id',
    AUTH_TOKEN: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4YjYxNTAyYmY3N2M5ZTc2MmYzMmRhNiIsInJvbGUiOiJ0ZWFjaGVyIiwiaWF0IjoxNzU3NzgyMTU5LCJleHAiOjE3NTc5NTQ5NTl9.8rMwJzn1X4uynj_H4baP_LpHLUjWt0DqfiWqSKMsQxE'
};

/**
 * Test comment creation with isDoubt=true
 */
async function testCommentCreation() {
    console.log('🧪 Testing comment creation with isDoubt=true...\n');
    
    const commentData = {
        videoId: CONFIG.TEST_VIDEO_ID,
        userId: CONFIG.TEST_USER_ID,
        username: 'Test User',
        content: 'This is a test doubt comment',
        isDoubt: true
    };

    try {
        console.log('📝 Creating comment in SAPS...');
        const response = await fetch(`${CONFIG.SAPS_API_URL}/video/comment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': CONFIG.AUTH_TOKEN
            },
            body: JSON.stringify(commentData)
        });

        const result = await response.json();
        console.log('✅ SAPS Response:', JSON.stringify(result, null, 2));

        if (result.id) {
            console.log(`\n🔍 Comment created with ID: ${result.id}`);
            return result.id;
        } else {
            console.error('❌ No comment ID returned from SAPS');
            return null;
        }
    } catch (error) {
        console.error('❌ Error creating comment:', error.message);
        return null;
    }
}

/**
 * Test CDS LessonMetrics to verify doubt was stored
 */
async function testCDSDoubtStorage(commentId) {
    console.log('\n🔍 Checking CDS LessonMetrics for doubt storage...\n');
    
    try {
        // This would need to be adapted based on your CDS API structure
        console.log('📊 Querying CDS for user lesson metrics...');
        
        // Note: You might need to create a specific endpoint to query lesson metrics
        // or use existing endpoints to verify the doubt was stored
        
        console.log('⚠️  Manual verification needed: Check CDS database directly for:');
        console.log(`   - userId: ${CONFIG.TEST_USER_ID}`);
        console.log(`   - videoId: ${CONFIG.TEST_VIDEO_ID}`);
        console.log(`   - commentId: ${commentId}`);
        console.log(`   - doubts array should contain: {commentId: "${commentId}", status: -1}`);
        
    } catch (error) {
        console.error('❌ Error checking CDS:', error.message);
    }
}

/**
 * Test teacher panel doubt retrieval
 */
async function testTeacherPanelRetrieval() {
    console.log('\n👩‍🏫 Testing teacher panel doubt retrieval...\n');
    
    try {
        console.log('📚 Fetching doubts from teacher panel endpoint...');
        const response = await fetch(
            `${CONFIG.SAPS_API_URL}/video/comments/group/${CONFIG.TEST_GROUP_ID}?isDoubt=true&limit=10`,
            {
                headers: {
                    'Authorization': CONFIG.AUTH_TOKEN
                }
            }
        );

        const result = await response.json();
        console.log('✅ Teacher Panel Response:', JSON.stringify(result, null, 2));
        
        if (result.comments && result.comments.length > 0) {
            console.log(`\n📊 Found ${result.comments.length} doubts`);
            result.comments.forEach((comment, index) => {
                console.log(`   ${index + 1}. ${comment.content} (Status: ${comment.doubtStatus})`);
            });
        } else {
            console.log('⚠️  No doubts found in teacher panel');
        }
        
    } catch (error) {
        console.error('❌ Error fetching teacher panel doubts:', error.message);
    }
}

/**
 * Test the complete flow
 */
async function runDebugFlow() {
    console.log('🚀 Starting Comment Creation Debug Flow\n');
    console.log('=' .repeat(50));
    
    // Step 1: Create a comment with isDoubt=true
    const commentId = await testCommentCreation();
    
    if (commentId) {
        // Step 2: Verify CDS storage
        await testCDSDoubtStorage(commentId);
        
        // Wait a bit for async processing
        console.log('\n⏳ Waiting 3 seconds for async processing...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Step 3: Test teacher panel retrieval
        await testTeacherPanelRetrieval();
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('🏁 Debug flow completed');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Update CONFIG values with your actual API URLs and tokens');
    console.log('2. Run this script: node debug_comment_creation.js');
    console.log('3. Check the console output for any failures in the flow');
    console.log('4. Manually verify CDS database if needed');
}

// Run the debug flow if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runDebugFlow().catch(console.error);
}

export { testCommentCreation, testCDSDoubtStorage, testTeacherPanelRetrieval };
