#!/usr/bin/env node

/**
 * Comprehensive test environment to reproduce and verify the doubt visibility issue
 * This script creates test data and verifies the complete flow
 */

import fetch from 'node-fetch';

// Test Configuration
const TEST_CONFIG = {
    // API URLs - Update with your actual endpoints
    SAPS_API_URL: process.env.SAPS_API_URL || 'https://your-saps-api-url.com',
    CDS_API_URL: process.env.CDS_API_URL || 'https://your-cds-api-url.com',
    
    // Test Data
    TEST_STUDENT: {
        id: 'test-student-001',
        name: 'Test Student',
        token: 'Bearer student-token'
    },
    TEST_TEACHER: {
        id: 'test-teacher-001', 
        name: 'Test Teacher',
        token: 'Bearer teacher-token'
    },
    TEST_VIDEO: {
        id: 12345,
        playlistId: 67890,
        title: 'Test Video for Doubts'
    },
    TEST_GROUP: {
        id: 'test-group-001',
        name: 'Test Group',
        members: ['test-student-001', 'test-teacher-001']
    }
};

/**
 * Test Environment Setup
 */
class DoubtTestEnvironment {
    constructor() {
        this.testResults = [];
        this.createdComments = [];
    }

    /**
     * Log test result
     */
    logResult(testName, success, message, data = null) {
        const result = {
            test: testName,
            success,
            message,
            data,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
        if (data && process.env.DEBUG) {
            console.log('   Data:', JSON.stringify(data, null, 2));
        }
    }

    /**
     * Test 1: Create a comment with isDoubt=true
     */
    async testCreateDoubtComment() {
        console.log('\n📝 Test 1: Creating doubt comment...');
        
        const commentData = {
            videoId: TEST_CONFIG.TEST_VIDEO.id,
            userId: TEST_CONFIG.TEST_STUDENT.id,
            username: TEST_CONFIG.TEST_STUDENT.name,
            content: `Test doubt comment created at ${new Date().toISOString()}`,
            isDoubt: true
        };

        try {
            const response = await fetch(`${TEST_CONFIG.SAPS_API_URL}/video/comment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': TEST_CONFIG.TEST_STUDENT.token
                },
                body: JSON.stringify(commentData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.id) {
                this.createdComments.push(result.id);
                this.logResult(
                    'Create Doubt Comment', 
                    true, 
                    `Comment created with ID: ${result.id}`,
                    result
                );
                return result.id;
            } else {
                this.logResult(
                    'Create Doubt Comment', 
                    false, 
                    'No comment ID returned',
                    result
                );
                return null;
            }
        } catch (error) {
            this.logResult(
                'Create Doubt Comment', 
                false, 
                error.message
            );
            return null;
        }
    }

    /**
     * Test 2: Verify comment exists in SAPS
     */
    async testVerifyCommentInSAPS(commentId) {
        console.log('\n🔍 Test 2: Verifying comment in SAPS...');
        
        try {
            const response = await fetch(
                `${TEST_CONFIG.SAPS_API_URL}/video/comment/${commentId}/info`,
                {
                    headers: {
                        'Authorization': TEST_CONFIG.TEST_STUDENT.token
                    }
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            this.logResult(
                'Verify Comment in SAPS', 
                true, 
                `Comment found in SAPS with isDoubt: ${result.isDoubt}`,
                result
            );
            return result;
        } catch (error) {
            this.logResult(
                'Verify Comment in SAPS', 
                false, 
                error.message
            );
            return null;
        }
    }

    /**
     * Test 3: Check if doubt was synced to CDS
     */
    async testVerifyDoubtInCDS(commentId) {
        console.log('\n🗄️  Test 3: Verifying doubt in CDS...');
        
        try {
            // This would require a specific endpoint to check lesson metrics
            // For now, we'll simulate this check
            console.log('⚠️  Manual verification needed for CDS:');
            console.log(`   - Check LessonMetrics for userId: ${TEST_CONFIG.TEST_STUDENT.id}`);
            console.log(`   - Check videoId: ${TEST_CONFIG.TEST_VIDEO.id}`);
            console.log(`   - Look for commentId: ${commentId} in doubts array`);
            console.log(`   - Verify status: -1 (pending)`);
            
            this.logResult(
                'Verify Doubt in CDS', 
                null, 
                'Manual verification required - check CDS database directly'
            );
            
            return null;
        } catch (error) {
            this.logResult(
                'Verify Doubt in CDS', 
                false, 
                error.message
            );
            return null;
        }
    }

    /**
     * Test 4: Test teacher panel retrieval
     */
    async testTeacherPanelRetrieval() {
        console.log('\n👩‍🏫 Test 4: Testing teacher panel retrieval...');
        
        try {
            const response = await fetch(
                `${TEST_CONFIG.SAPS_API_URL}/video/comments/group/${TEST_CONFIG.TEST_GROUP.id}?isDoubt=true&limit=10`,
                {
                    headers: {
                        'Authorization': TEST_CONFIG.TEST_TEACHER.token
                    }
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            const doubtsFound = result.comments ? result.comments.length : 0;
            
            this.logResult(
                'Teacher Panel Retrieval', 
                doubtsFound > 0, 
                `Found ${doubtsFound} doubts in teacher panel`,
                result
            );
            
            return result.comments || [];
        } catch (error) {
            this.logResult(
                'Teacher Panel Retrieval', 
                false, 
                error.message
            );
            return [];
        }
    }

    /**
     * Test 5: End-to-end verification
     */
    async testEndToEndFlow() {
        console.log('\n🔄 Test 5: End-to-end flow verification...');
        
        // Wait a bit for async processing
        console.log('⏳ Waiting 5 seconds for async processing...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        const doubts = await this.testTeacherPanelRetrieval();
        const createdCommentFound = doubts.some(doubt => 
            this.createdComments.includes(doubt.id?.toString())
        );
        
        this.logResult(
            'End-to-End Flow', 
            createdCommentFound, 
            createdCommentFound 
                ? 'Created doubt appears in teacher panel' 
                : 'Created doubt NOT found in teacher panel'
        );
        
        return createdCommentFound;
    }

    /**
     * Generate test report
     */
    generateReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 TEST REPORT');
        console.log('='.repeat(60));
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success === true).length;
        const failedTests = this.testResults.filter(r => r.success === false).length;
        const skippedTests = this.testResults.filter(r => r.success === null).length;
        
        console.log(`Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`⚠️  Skipped: ${skippedTests}`);
        
        console.log('\nDetailed Results:');
        this.testResults.forEach((result, index) => {
            const status = result.success === true ? '✅' : result.success === false ? '❌' : '⚠️';
            console.log(`${index + 1}. ${status} ${result.test}: ${result.message}`);
        });
        
        console.log('\n📋 Recommendations:');
        if (failedTests > 0) {
            console.log('1. Check API endpoints and authentication tokens');
            console.log('2. Verify SAPS → CDS synchronization is working');
            console.log('3. Check group membership and permissions');
            console.log('4. Review error logs in both SAPS and CDS services');
        } else {
            console.log('1. All tests passed - system appears to be working correctly');
            console.log('2. If doubts still not appearing, check production data');
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            skipped: skippedTests,
            results: this.testResults
        };
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting Comprehensive Doubt System Test');
        console.log('='.repeat(60));
        
        console.log('📋 Test Configuration:');
        console.log(`   SAPS API: ${TEST_CONFIG.SAPS_API_URL}`);
        console.log(`   CDS API: ${TEST_CONFIG.CDS_API_URL}`);
        console.log(`   Test Student: ${TEST_CONFIG.TEST_STUDENT.id}`);
        console.log(`   Test Teacher: ${TEST_CONFIG.TEST_TEACHER.id}`);
        console.log(`   Test Group: ${TEST_CONFIG.TEST_GROUP.id}`);
        console.log(`   Test Video: ${TEST_CONFIG.TEST_VIDEO.id}`);
        
        // Test 1: Create doubt comment
        const commentId = await this.testCreateDoubtComment();
        
        if (commentId) {
            // Test 2: Verify in SAPS
            await this.testVerifyCommentInSAPS(commentId);
            
            // Test 3: Check CDS sync
            await this.testVerifyDoubtInCDS(commentId);
            
            // Test 4 & 5: Teacher panel and end-to-end
            await this.testEndToEndFlow();
        } else {
            console.log('⚠️  Skipping remaining tests due to comment creation failure');
        }
        
        return this.generateReport();
    }
}

// Run tests if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const testEnv = new DoubtTestEnvironment();
    testEnv.runAllTests().catch(console.error);
}

export default DoubtTestEnvironment;
