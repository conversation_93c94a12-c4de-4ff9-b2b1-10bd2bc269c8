#!/usr/bin/env node

// Conecta ao banco de dados SAPS e busca o comentário específico
// Primeiro, vamos olhar a estrutura do banco

console.log('Iniciando busca do comentário...');

async function findComment() {
    let connection;
    
    try {
        // Conectar ao banco
        connection = await mysql.createConnection({
            host: DB_HOST,
            user: DB_USER,
            password: DB_PASSWORD,
            database: DB_NAME
        });
        
        console.log('Conectado ao banco de dados SAPS');
        
        // Buscar o comentário específico
        const [commentRows] = await connection.execute(
            'SELECT * FROM VideoComment WHERE id = ?',
            [43]
        );
        
        if (commentRows.length === 0) {
            console.log('Comentário com ID 43 não encontrado');
            return;
        }
        
        const comment = commentRows[0];
        console.log('\n=== COMENTÁRIO ENCONTRADO ===');
        console.log('ID:', comment.id);
        console.log('Conteúdo:', comment.content);
        console.log('User ID:', comment.userId);
        console.log('Username:', comment.username);
        console.log('Video ID:', comment.videoId);
        console.log('isDoubt:', comment.isDoubt);
        console.log('Created At:', comment.createdAt);
        
        // Buscar informações do vídeo
        const [videoRows] = await connection.execute(
            'SELECT * FROM Video WHERE id = ?',
            [comment.videoId]
        );
        
        if (videoRows.length > 0) {
            const video = videoRows[0];
            console.log('\n=== VÍDEO ASSOCIADO ===');
            console.log('Video ID:', video.id);
            console.log('Title:', video.title);
            console.log('Description:', video.description);
            console.log('Playlist ID:', video.playlistId);
        }
        
        // Buscar informações da playlist
        if (videoRows.length > 0 && videoRows[0].playlistId) {
            const [playlistRows] = await connection.execute(
                'SELECT * FROM Playlist WHERE id = ?',
                [videoRows[0].playlistId]
            );
            
            if (playlistRows.length > 0) {
                const playlist = playlistRows[0];
                console.log('\n=== PLAYLIST ASSOCIADA ===');
                console.log('Playlist ID:', playlist.id);
                console.log('Name:', playlist.name);
                console.log('Group ID:', playlist.groupId);
                console.log('Subject:', playlist.subject);
                console.log('Created By:', playlist.createdBy);
            }
        }
        
    } catch (error) {
        console.error('Erro ao conectar ou consultar o banco:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

findComment();