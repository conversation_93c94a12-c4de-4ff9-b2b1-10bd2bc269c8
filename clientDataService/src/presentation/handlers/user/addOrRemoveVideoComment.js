import addOrRemoveVideoCommentMetricsService from '../../../application/services/lessonMetrics/addOrRemoveVideoCommentMetricsService.js';
import { CustomError } from "../../../utils/customErrors/index.js";
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();
    const body = JSON.parse(event.body);
    const { id } = event.pathParameters; // Pegar o id do path parameter
    const { videoId, commentId, isDoubt, isRemove, playlistId } = body;

    console.log("CDS addOrRemoveVideoComment - Request details:", {
      userId: id,
      videoId,
      commentId,
      isDoubt,
      isRemove,
      playlistId,
      timestamp: new Date().toISOString()
    });

    // Validate required parameters
    if (!id) {
      throw new Error('userId is required');
    }
    if (!videoId) {
      throw new Error('videoId is required');
    }
    if (!commentId) {
      throw new Error('commentId is required');
    }

    const updatedData = await addOrRemoveVideoCommentMetricsService({
      userId: id,
      videoId,
      commentId,
      isDoubt,
      isRemove,
      playlistId
    });

    console.log("CDS addOrRemoveVideoComment - Success:", {
      userId: id,
      commentId,
      isDoubt,
      result: updatedData
    });

    return apiResponse(200, { body: updatedData });
  } catch (error) {
    console.error("CDS addOrRemoveVideoComment - Error:", {
      error: error.message,
      stack: error.stack,
      userId: event.pathParameters?.id,
      body: event.body
    });

    if (error instanceof CustomError) {
      return apiResponse(error.code, { body: { message: error.message } });
    }

    return apiResponse(500, { body: { message: error.message } });
  }
}