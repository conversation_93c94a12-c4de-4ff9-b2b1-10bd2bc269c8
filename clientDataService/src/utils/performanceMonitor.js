/**
 * Performance Monitoring Utility
 * 
 * Provides performance tracking and metrics collection for the comment system
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.activeTimers = new Map();
        this.thresholds = {
            slow_query: 1000,      // 1 second
            very_slow_query: 5000, // 5 seconds
            cache_hit_ratio: 0.8,  // 80% cache hit ratio target
            api_timeout: 10000     // 10 seconds API timeout
        };
    }

    /**
     * Start timing an operation
     */
    startTimer(operationId, metadata = {}) {
        const timer = {
            startTime: Date.now(),
            startHrTime: process.hrtime.bigint(),
            metadata
        };
        
        this.activeTimers.set(operationId, timer);
        console.log(`⏱️  [PERF] Started timing: ${operationId}`);
        return operationId;
    }

    /**
     * End timing an operation and record metrics
     */
    endTimer(operationId, additionalMetadata = {}) {
        const timer = this.activeTimers.get(operationId);
        if (!timer) {
            console.warn(`⚠️  [PERF] Timer not found: ${operationId}`);
            return null;
        }

        const endTime = Date.now();
        const endHrTime = process.hrtime.bigint();
        
        const duration = endTime - timer.startTime;
        const precisionDuration = Number(endHrTime - timer.startHrTime) / 1000000; // Convert to milliseconds

        const metric = {
            operationId,
            duration,
            precisionDuration,
            startTime: timer.startTime,
            endTime,
            metadata: { ...timer.metadata, ...additionalMetadata }
        };

        // Store metric
        if (!this.metrics.has(operationId)) {
            this.metrics.set(operationId, []);
        }
        this.metrics.get(operationId).push(metric);

        // Clean up active timer
        this.activeTimers.delete(operationId);

        // Log performance warning if slow
        this.logPerformanceWarning(operationId, duration, metric);

        console.log(`✅ [PERF] Completed: ${operationId} in ${duration}ms`);
        return metric;
    }

    /**
     * Log performance warnings for slow operations
     */
    logPerformanceWarning(operationId, duration, metric) {
        if (duration > this.thresholds.very_slow_query) {
            console.warn(`🐌 [PERF] VERY SLOW operation: ${operationId} took ${duration}ms`, metric.metadata);
        } else if (duration > this.thresholds.slow_query) {
            console.warn(`⚠️  [PERF] Slow operation: ${operationId} took ${duration}ms`, metric.metadata);
        }
    }

    /**
     * Record cache performance metrics
     */
    recordCacheMetrics(operation, hits, misses, totalRequests) {
        const hitRatio = totalRequests > 0 ? hits / totalRequests : 0;
        
        const cacheMetric = {
            operation,
            hits,
            misses,
            totalRequests,
            hitRatio,
            timestamp: Date.now()
        };

        const cacheKey = `cache_${operation}`;
        if (!this.metrics.has(cacheKey)) {
            this.metrics.set(cacheKey, []);
        }
        this.metrics.get(cacheKey).push(cacheMetric);

        // Log cache performance
        if (hitRatio < this.thresholds.cache_hit_ratio) {
            console.warn(`📊 [PERF] Low cache hit ratio for ${operation}: ${(hitRatio * 100).toFixed(1)}% (${hits}/${totalRequests})`);
        } else {
            console.log(`📊 [PERF] Cache performance for ${operation}: ${(hitRatio * 100).toFixed(1)}% hit ratio (${hits}/${totalRequests})`);
        }

        return cacheMetric;
    }

    /**
     * Record database query metrics
     */
    recordDatabaseMetrics(queryType, documentsExamined, documentsReturned, executionTime, indexUsed = null) {
        const efficiency = documentsReturned > 0 ? documentsReturned / documentsExamined : 0;
        
        const dbMetric = {
            queryType,
            documentsExamined,
            documentsReturned,
            executionTime,
            indexUsed,
            efficiency,
            timestamp: Date.now()
        };

        const dbKey = `db_${queryType}`;
        if (!this.metrics.has(dbKey)) {
            this.metrics.set(dbKey, []);
        }
        this.metrics.get(dbKey).push(dbMetric);

        // Log database performance warnings
        if (efficiency < 0.1 && documentsExamined > 100) {
            console.warn(`🗃️  [PERF] Inefficient query ${queryType}: examined ${documentsExamined}, returned ${documentsReturned} (${(efficiency * 100).toFixed(1)}% efficiency)`);
        }

        if (!indexUsed && documentsExamined > 50) {
            console.warn(`🗃️  [PERF] Query ${queryType} performed collection scan on ${documentsExamined} documents`);
        }

        return dbMetric;
    }

    /**
     * Record API call metrics
     */
    recordApiMetrics(endpoint, method, statusCode, responseTime, requestSize = 0, responseSize = 0) {
        const apiMetric = {
            endpoint,
            method,
            statusCode,
            responseTime,
            requestSize,
            responseSize,
            success: statusCode >= 200 && statusCode < 300,
            timestamp: Date.now()
        };

        const apiKey = `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
        if (!this.metrics.has(apiKey)) {
            this.metrics.set(apiKey, []);
        }
        this.metrics.get(apiKey).push(apiMetric);

        // Log API performance warnings
        if (responseTime > this.thresholds.api_timeout) {
            console.warn(`🌐 [PERF] Slow API call: ${method} ${endpoint} took ${responseTime}ms`);
        }

        if (!apiMetric.success) {
            console.warn(`🌐 [PERF] Failed API call: ${method} ${endpoint} returned ${statusCode}`);
        }

        return apiMetric;
    }

    /**
     * Get performance summary for an operation
     */
    getOperationSummary(operationId) {
        const metrics = this.metrics.get(operationId);
        if (!metrics || metrics.length === 0) {
            return null;
        }

        const durations = metrics.map(m => m.duration);
        const summary = {
            operationId,
            count: metrics.length,
            totalTime: durations.reduce((sum, d) => sum + d, 0),
            averageTime: durations.reduce((sum, d) => sum + d, 0) / durations.length,
            minTime: Math.min(...durations),
            maxTime: Math.max(...durations),
            medianTime: this.calculateMedian(durations),
            p95Time: this.calculatePercentile(durations, 95),
            p99Time: this.calculatePercentile(durations, 99)
        };

        return summary;
    }

    /**
     * Get overall performance report
     */
    getPerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            operations: {},
            cacheMetrics: {},
            databaseMetrics: {},
            apiMetrics: {}
        };

        // Process all metrics
        for (const [key, metrics] of this.metrics.entries()) {
            if (key.startsWith('cache_')) {
                const operation = key.replace('cache_', '');
                report.cacheMetrics[operation] = this.summarizeCacheMetrics(metrics);
            } else if (key.startsWith('db_')) {
                const queryType = key.replace('db_', '');
                report.databaseMetrics[queryType] = this.summarizeDatabaseMetrics(metrics);
            } else if (key.startsWith('api_')) {
                const endpoint = key.replace('api_', '');
                report.apiMetrics[endpoint] = this.summarizeApiMetrics(metrics);
            } else {
                report.operations[key] = this.getOperationSummary(key);
            }
        }

        return report;
    }

    /**
     * Summarize cache metrics
     */
    summarizeCacheMetrics(metrics) {
        const totalHits = metrics.reduce((sum, m) => sum + m.hits, 0);
        const totalMisses = metrics.reduce((sum, m) => sum + m.misses, 0);
        const totalRequests = totalHits + totalMisses;
        
        return {
            totalRequests,
            totalHits,
            totalMisses,
            overallHitRatio: totalRequests > 0 ? totalHits / totalRequests : 0,
            samples: metrics.length
        };
    }

    /**
     * Summarize database metrics
     */
    summarizeDatabaseMetrics(metrics) {
        const totalExamined = metrics.reduce((sum, m) => sum + m.documentsExamined, 0);
        const totalReturned = metrics.reduce((sum, m) => sum + m.documentsReturned, 0);
        const avgExecutionTime = metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length;
        
        return {
            totalQueries: metrics.length,
            totalDocumentsExamined: totalExamined,
            totalDocumentsReturned: totalReturned,
            averageExecutionTime: avgExecutionTime,
            overallEfficiency: totalReturned > 0 ? totalReturned / totalExamined : 0
        };
    }

    /**
     * Summarize API metrics
     */
    summarizeApiMetrics(metrics) {
        const successfulCalls = metrics.filter(m => m.success).length;
        const avgResponseTime = metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length;
        
        return {
            totalCalls: metrics.length,
            successfulCalls,
            failedCalls: metrics.length - successfulCalls,
            successRate: metrics.length > 0 ? successfulCalls / metrics.length : 0,
            averageResponseTime: avgResponseTime
        };
    }

    /**
     * Calculate median of an array
     */
    calculateMedian(arr) {
        const sorted = [...arr].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
    }

    /**
     * Calculate percentile of an array
     */
    calculatePercentile(arr, percentile) {
        const sorted = [...arr].sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[Math.max(0, index)];
    }

    /**
     * Clear all metrics (useful for testing)
     */
    clearMetrics() {
        this.metrics.clear();
        this.activeTimers.clear();
        console.log('🧹 [PERF] Cleared all performance metrics');
    }

    /**
     * Export metrics to JSON
     */
    exportMetrics() {
        const report = this.getPerformanceReport();
        return JSON.stringify(report, null, 2);
    }
}

// Singleton instance
const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;
