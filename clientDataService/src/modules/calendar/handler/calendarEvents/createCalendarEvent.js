import { createCalendarEvent } from '../../useCases/createCalendarEvent.js';
import { ValidationError, AuthorizationError } from '../../../../utils/customErrors/index.js';
import { apiResponse } from '../../../../utils/apiResponse.js';

export async function handler(event) {
  try {
    console.log("[debug] event", event);
    console.log("[debug] event.body", event.body);
    // Validate event structure
    if (!event || !event.body) {
      throw new ValidationError('Request body is required');
    }

    // Parse and validate request body
    let body;
    try {
      // only parse the body if it is a string
      if (typeof event.body === 'string') {
        body = JSON.parse(event.body);
      } else {
        body = event.body;
      }
    } catch (error) {
      console.log("[debug] error", error);
      throw new ValidationError('Invalid JSON in request body');
    }

    // Validate authentication context
    const { id: userId, role } = event.requestContext.authorizer || {};
    if (!userId) {
      throw new AuthorizationError('Authentication required');
    }

    // Extract and validate required parameters
    const { 
      ownerUsername,
      type, 
      contentData, 
      startDate, 
      endDate, 
      isContinuous, 
      groupId,
      isGlobal
    } = body;
    
    // Basic validation of required fields
    if (!ownerUsername) {
      throw new ValidationError('ownerUsername is required');
    }

    if (!type) {
      throw new ValidationError('type is required');
    }

    if (!contentData) {
      throw new ValidationError('contentData is required');
    }

    if (!startDate) {
      throw new ValidationError('startDate is required');
    }
    
    const result = await createCalendarEvent({
      userId,
      role,
      ownerUsername,
      type,
      contentData,
      startDate,
      endDate,
      isContinuous,
      groupId,
      isGlobal
    });

    return apiResponse(201, result);
  } catch (error) {
    console.error('Error in createCalendarEvent handler:', error);
    
    // Handle specific error types
    if (error instanceof ValidationError) {
      return apiResponse(400, {
        success: false,
        message: error.message
      });
    }
    
    if (error instanceof AuthorizationError) {
      // Handle authentication vs authorization errors
      const statusCode = error.message.includes('Authentication required') ? 401 : 403;
      return apiResponse(statusCode, {
        success: false,
        message: error.message
      });
    }
    
    // Handle unexpected errors
    return apiResponse(500, {
      success: false,
      message: 'Internal server error'
    });
  }
} 