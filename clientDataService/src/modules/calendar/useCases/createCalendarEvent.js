import calendarEventRepository from '../repositories/calendarEventRepository.js';
import { CalendarEventAggregate } from '../aggregate/calendarEventAggregate.js';
import { fetchUserGroups } from '../../groups/useCases/fetchUserGroups.js';
import { ValidationError, AuthorizationError } from '../../../utils/customErrors/index.js';

export const createCalendarEvent = async ({ 
  userId, 
  role, 
  ownerUsername,
  type, 
  contentData, 
  startDate, 
  endDate, 
  isContinuous, 
  groupId,
  isGlobal 
}) => {
  try {
    // Input validation
    if (!userId) {
      throw new ValidationError('userId is required');
    }
    
    if (!ownerUsername) {
      throw new ValidationError('ownerUsername is required');
    }
    
    if (!type) {
      throw new ValidationError('type is required');
    }
    
    if (!contentData) {
      throw new ValidationError('contentData is required');
    }
    
    if (!startDate) {
      throw new ValidationError('startDate is required');
    }
    
    if (isContinuous && !endDate) {
      throw new ValidationError('endDate is required when isContinuous is true');
    }

    // Validate based on role
    if (role === 'student') {
      // Students can only create events for themselves
      if (groupId) {
        throw new AuthorizationError('Students can only create events for themselves, not for groups');
      }
    } else if (groupId) {
      // For non-students, if groupId is provided, verify the user is part of the group
      try {
        const { data: userGroups } = await fetchUserGroups({ userId });
        const isPartOfGroup = userGroups.some(group => group._id.toString() === groupId);
        
        if (!isPartOfGroup) {
          throw new AuthorizationError('You can only create events for groups you are a member of');
        }
      } catch (error) {
        if (error instanceof AuthorizationError) {
          throw error;
        }
        throw new Error(`Error verifying group membership: ${error.message}`);
      }
    }
    let isGlobal = isGlobal || null;
    if( role === 'student') {
      isGlobal = false;
    }
    
    // Create event data
    const eventData = {
      ownerId: userId,
      ownerUsername,
      type,
      contentData,
      startDate,
      endDate,
      isContinuous,
      groupId,  
      isGlobal
    };
    console.log("[debug] eventData", eventData);
    // Validate with aggregate using fromData method
    let eventAggregate;
    try {
      eventAggregate = CalendarEventAggregate.fromData(eventData);
      console.log("[debug] eventAggregate", eventAggregate);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError(`Event validation failed: ${error.message}`);
    }
    
    // Save to database using the validated data
    let createdEvent;
    try {
      createdEvent = await calendarEventRepository.create(eventAggregate.toData());
    } catch (error) {
      throw new Error(`Failed to create calendar event: ${error.message}`);
    }
    
    return {
      success: true,
      message: 'Calendar event created successfully',
      data: createdEvent
    };
  } catch (error) {
    // Re-throw specific errors
    if (error instanceof ValidationError || error instanceof AuthorizationError) {
      throw error;
    }
    
    // Log unexpected errors
    console.error('Unexpected error in createCalendarEvent:', error);
    throw new Error(`Failed to create calendar event: ${error.message}`);
  }
}; 