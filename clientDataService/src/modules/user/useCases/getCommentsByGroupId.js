import { LessonMetricModel } from '../../../domain/models/metrics/lessonMetricsModel.js';
import { GroupModel } from '../../groups/schema/groupSchema.js';
import { ValidationError, NotFoundError, AuthorizationError } from '../../../utils/customErrors/index.js';
import SapsApi from '../../../infra/api/SapsApi.js';
import commentCache from '../../../infra/cache/commentCache.js';

/**
 * Get comments by group ID (for teachers to see all comments from their students)
 * @param {Object} params - Parameters
 * @param {string} params.groupId - Group identifier
 * @param {string} params.videoId - Optional video identifier to filter comments
 * @param {boolean} params.isDoubt - Whether to filter only doubts
 * @param {number} params.page - Page number for pagination
 * @param {number} params.limit - Items per page
 * @param {string} params.userId - The user's ID
 * @param {string} params.token - Authentication token for SAPS calls
 * @returns {Promise<Object>} Comments with pagination info
 */
export async function getCommentsByGroupId({ 
    groupId, 
    videoId, 
    isDoubt = false, 
    page = 1, 
    limit = 20, 
    userId,
    token = null
}) {
    // Validate input and get group with authorization check
    if (!groupId) throw new ValidationError('groupId is required');
    
    const group = await GroupModel.findById(groupId);
    if (!group) throw new NotFoundError('Group not found');
    if (!group.members.includes(userId)) {
        throw new AuthorizationError('The user is not a member of this group');
    }

    const memberIds = group.members;
    const groupInfo = {
        groupId: group._id,
        groupName: group.name,
        category: group.category,
        membersCount: memberIds.length
    };

    // Return empty result if no members
    if (!memberIds?.length) {
        return createEmptyResponse(page, limit, groupInfo);
    }

    // Build query criteria
    const matchCriteria = buildMatchCriteria(memberIds, videoId, isDoubt);
    
    // Execute queries in parallel
    const [results, totalCount] = await Promise.all([
        getLessonMetrics(matchCriteria, page, limit),
        LessonMetricModel.countDocuments(matchCriteria)
    ]);

    // Process and enrich comments
    const processedComments = extractComments(results, isDoubt);
    const enrichedComments = await enrichCommentsWithSapsData(processedComments, token);

    return {
        comments: enrichedComments,
        pagination: createPaginationInfo(page, limit, totalCount),
        groupInfo
    };
}

/**
 * Creates empty response structure
 */
function createEmptyResponse(page, limit, groupInfo) {
    return {
        comments: [],
        pagination: createPaginationInfo(page, limit, 0),
        groupInfo: { ...groupInfo, membersCount: 0 }
    };
}

/**
 * Builds MongoDB match criteria based on filters
 */
function buildMatchCriteria(memberIds, videoId, isDoubt) {
    const criteria = { userId: { $in: memberIds } };
    
    if (videoId) criteria.videoId = parseInt(videoId);
    
    if (isDoubt) {
        criteria.doubts = { $elemMatch: { status: -1 } };
    } else {
        criteria.comments = { $exists: true, $not: { $size: 0 } };
    }
    
    return criteria;
}

/**
 * Gets lesson metrics with user data using aggregation pipeline
 */
async function getLessonMetrics(matchCriteria, page, limit) {
    const skip = (page - 1) * limit;
    
    return LessonMetricModel.aggregate([
        { $match: matchCriteria },
        {
            $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'user',
                pipeline: [{ $project: { name: 1, email: 1, role: 1 } }]
            }
        },
        { $unwind: '$user' },
        {
            $project: {
                userId: 1,
                videoId: 1,
                playlistId: 1,
                comments: 1,
                doubts: 1,
                createdAt: 1,
                updatedAt: 1,
                user: 1
            }
        },
        { $sort: { updatedAt: -1 } },
        { $skip: skip },
        { $limit: limit }
    ]);
}

/**
 * Extracts comments from lesson metrics based on type (doubt or regular comment)
 */
function extractComments(results, isDoubt) {
    const comments = [];
    
    for (const metric of results) {
        const baseComment = {
            userId: metric.userId,
            videoId: metric.videoId,
            playlistId: metric.playlistId,
            user: metric.user,
            createdAt: metric.createdAt,
            updatedAt: metric.updatedAt
        };

        if (isDoubt && metric.doubts) {
            // Add pending doubts (status -1)
            metric.doubts
                .filter(doubt => doubt.status === -1)
                .forEach(doubt => {
                    comments.push({
                        ...baseComment,
                        commentId: doubt.commentId,
                        isDoubt: true,
                        doubtStatus: doubt.status
                    });
                });
        } else if (!isDoubt && metric.comments) {
            // Add regular comments
            metric.comments.forEach(commentId => {
                comments.push({
                    ...baseComment,
                    commentId,
                    isDoubt: false
                });
            });
        }
    }
    
    return comments;
}

/**
 * Enriches comments with SAPS data in parallel with caching optimization
 */
async function enrichCommentsWithSapsData(comments, token) {
    if (!token) {
        console.warn('No token provided for SAPS enrichment, returning comments without content');
        return comments;
    }

    // Extract comment IDs for batch cache lookup
    const commentIds = comments.map(c => c.commentId).filter(Boolean);

    // Check cache for existing comment data
    const { cached, missing } = commentCache.batchGetCachedComments(commentIds);

    console.log(`Cache performance: ${Object.keys(cached).length} hits, ${missing.length} misses out of ${commentIds.length} total`);

    // Fetch missing comments from SAPS in parallel
    const fetchPromises = missing.map(async (commentId) => {
        try {
            const commentInfo = await SapsApi.getCommentInfo({ commentId, token });
            if (commentInfo) {
                // Cache the fetched comment data
                commentCache.cacheCommentInfo(commentId, commentInfo);
                return { commentId, data: commentInfo };
            }
        } catch (error) {
            console.error(`Failed to fetch comment ${commentId} from SAPS:`, error.message);
            return { commentId, error: error.message };
        }
        return { commentId, data: null };
    });

    const fetchResults = await Promise.allSettled(fetchPromises);

    // Combine cached and fetched data
    const allCommentData = { ...cached };
    fetchResults.forEach(result => {
        if (result.status === 'fulfilled' && result.value.data) {
            allCommentData[result.value.commentId] = result.value.data;
        }
    });

    // Enrich comments with available data
    return comments.map(comment => {
        if (!comment.commentId) {
            console.warn('Comment missing commentId, cannot enrich with SAPS data:', comment);
            return comment;
        }

        const commentInfo = allCommentData[comment.commentId];

        if (commentInfo && commentInfo.content) {
            // Enhanced videoId resolution with multiple fallbacks
            const resolvedVideoId = commentInfo.videoId || comment.videoId || commentInfo.video?.id;

            // Log videoId resolution for debugging
            if (commentInfo.videoId !== comment.videoId && comment.videoId) {
                console.log(`VideoId mismatch resolved for comment ${comment.commentId}: SAPS=${commentInfo.videoId}, CDS=${comment.videoId}, resolved=${resolvedVideoId}`);
            }

            return {
                ...commentInfo,
                doubtStatus: comment.doubtStatus,
                // Use enhanced videoId resolution: SAPS videoId -> CDS videoId -> SAPS video.id
                videoId: resolvedVideoId
            };
        } else {
            console.warn(`No valid data available for comment ${comment.commentId}, using fallback`);

            // Determine the reason for enrichment failure and provide appropriate fallback
            let fallbackContent;
            const commentId = comment.commentId;

            // Check if the comment ID is invalid (non-numeric)
            if (isNaN(parseInt(commentId))) {
                fallbackContent = `[Invalid comment ID: ${commentId} - This comment may be orphaned data]`;
                console.warn(`Orphaned comment detected in CDS: ${commentId}. This comment ID doesn't exist in SAPS.`);
            } else {
                fallbackContent = `[Comment content temporarily unavailable for ID ${commentId}]`;
                console.warn(`SAPS enrichment failed for valid comment ID: ${commentId}. Comment may have been deleted from SAPS.`);
            }

            // Return comment with appropriate fallback content
            return {
                ...comment,
                content: fallbackContent,
                id: comment.commentId,
                videoId: comment.videoId,
                userId: comment.userId,
                username: comment.user?.name || 'Unknown User',
                createdAt: comment.createdAt,
                updatedAt: comment.updatedAt,
                video: null // Explicitly set video to null when enrichment fails
            };
        }
    });
}

/**
 * Creates pagination information object
 */
function createPaginationInfo(page, limit, total) {
    const totalPages = Math.ceil(total / limit);
    
    return {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1
    };
}