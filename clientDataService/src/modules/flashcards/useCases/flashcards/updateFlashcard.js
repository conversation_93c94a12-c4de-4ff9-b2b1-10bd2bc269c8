import FlashcardRepository from '../../repositories/flashcardRepository.js';
import { NotFoundError, ValidationError } from '../../../../utils/customErrors/index.js';

/**
 * Update an existing flashcard use case
 * @param {string} flashcardId - The ID of the flashcard to update
 * @param {Object} updateData - The data to update
 * @param {string} [updateData.question] - The updated question
 * @param {string} [updateData.answer] - The updated answer
 * @param {string[]} [updateData.tags] - Updated array of tags
 * @param {string[]} [updateData.olympiadIds] - Updated array of olympiad IDs
 * @param {number} [updateData.difficulty] - Updated difficulty level (1, 2, or 3)
 * @param {string} userId - The ID of the user making the update (must be the owner)
 * @returns {Promise<Object>} The updated flashcard aggregate
 */
async function updateFlashcard(flashcardId, updateData, userId) {
  try {
    // Get the flashcard to check ownership
    const flashcard = await FlashcardRepository.findById({flashcardId, userId});
    
    // Check if the user is the owner
    if (flashcard.ownerId.toString() !== userId) {
      throw new ValidationError('You do not have permission to update this flashcard');
    }
    
    // Validate difficulty if provided
    if (updateData.difficulty && ![1, 2, 3].includes(updateData.difficulty)) {
      throw new ValidationError('Difficulty must be 1, 2, or 3');
    }

    updateData.isFromAi = false;

    // Update the flashcard
    const updatedFlashcard = await FlashcardRepository.update({flashcardId, updateData});

    return updatedFlashcard;
  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }
    throw new Error(`Error updating flashcard: ${error.message}`);
  }
}

export default updateFlashcard; 