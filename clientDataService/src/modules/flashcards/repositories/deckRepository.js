import mongoose from 'mongoose';
import { DeckAggregate } from '../aggregate/deckAggregate.js';
import Deck from '../model/flashcardDeckSchema.js';
import Flashcard from '../model/flashcardSchema.js';
import FlashcardStatistics from '../model/flashcardStatisticsSchema.js';
import { NotFoundError, ResourceAlreadyExistsError, ValidationError } from '../../../utils/customErrors/index.js';

class DeckRepository {
  /**
   * Creates a new flashcard deck in the database.
   * @param {Object} deckData - The data for the new deck.
   * @returns {Promise<DeckAggregate>} - The created deck aggregate.
   */
  static async create({deckData}) {
    try {
      const deck = new Deck({
        name: deckData.name,
        description: deckData.description,
        ownerId: deckData.ownerId,
        shareLinkId: deckData.shareLinkId,
        flashcards: [],
        statistics: {
          numberOfFlashcards: 0,
          numberOfTimesAnswered: 0,
          numberOfCorrectAnswers: 0,
          numberOfIncorrectAnswers: 0
        }
      });
      
      await deck.save();
      
      return DeckAggregate.fromData({
        id: deck._id,
        name: deck.name,
        description: deck.description,
        flashcards: [],
        shareLinkId: deck.shareLinkId,
        ownerId: deck.ownerId
      });
    } catch (error) {
      throw new Error(`Error creating deck: ${error.message}`);
    }
  }

  /**
   * Updates an existing deck.
   * @param {string} deckId - The ID of the deck to update.
   * @param {Object} updateData - The data to update in the deck.
   * @returns {Promise<DeckAggregate>} - The updated deck aggregate.
   */
  static async update({deckId, updateData}) {
    try {
      const deck = await Deck.findById(deckId);
      if (!deck) {
        throw new NotFoundError('Deck not found.');
      }

      // Only update name and description
      if (updateData.name) deck.name = updateData.name;
      if (updateData.description) deck.description = updateData.description;
      
      await deck.save();
      
      // Get flashcards with their full data
      const flashcardsWithData = await Promise.all(
        deck.flashcards.map(async (deckFlashcard) => {
          const flashcard = await Flashcard.findById(deckFlashcard.flashcardId);
          return {
            flashcard: {
              id: flashcard._id,
              ...flashcard.toObject()
            },
            sortOrder: deckFlashcard.sortOrder
          };
        })
      );
      
      return DeckAggregate.fromData({
        id: deck._id,
        name: deck.name,
        description: deck.description,
        flashcards: flashcardsWithData,
        ownerId: deck.ownerId
      });
    } catch (error) {
      throw new Error(`Error updating deck: ${error.message}`);
    }
  }

  /**
   * Adds a flashcard to a deck.
   * @param {string} deckId - The ID of the deck.
   * @param {Object} deckData - The data of the new Deck.
   * @returns {Promise<DeckAggregate>} - The updated deck aggregate.
   */
  static async updateContent({deckId, deckData, session}) {
    console.log({deckData})
    const {ownerId, shareLink,...parsedDeckData } = deckData;
    const updatedDeck = session ? await Deck.updateOne({_id: deckId}, parsedDeckData).session(session) : await Deck.updateOne({_id: deckId}, parsedDeckData);
    console.log({updatedDeck})
    if (!updatedDeck) return false
    return true
  }

  /**
   * Removes a flashcard from a deck.
   * @param {string} deckId - The ID of the deck.
   * @param {string} flashcardId - The ID of the flashcard to remove.
   * @returns {Promise<DeckAggregate>} - The updated deck aggregate.
   */
  static async removeFlashcard({deckId, flashcardId}) {
    const session = await mongoose.startSession();
    session.startTransaction();
    
    try {
      const deck = await Deck.findById(deckId).session(session);
      if (!deck) {
        throw new NotFoundError('Deck not found.');
      }
      
      // Find the flashcard in the deck
      const flashcardIndex = deck.flashcards.findIndex(
        f => f.flashcardId.toString() === flashcardId
      );
      
      if (flashcardIndex === -1) {
        throw new NotFoundError('Flashcard not found in this deck.');
      }
      
      // Remove the flashcard
      deck.flashcards.splice(flashcardIndex, 1);
      
      // Update sort orders for remaining flashcards
      deck.flashcards.forEach((f, idx) => {
        if (f.sortOrder > flashcardIndex) {
          f.sortOrder -= 1;
        }
      });
      
      // Update the deck's statistics
      deck.statistics.numberOfFlashcards -= 1;
      
      await deck.save({ session });
      await session.commitTransaction();
      
      // Get all flashcards with their full data
      const flashcardsWithData = await Promise.all(
        deck.flashcards.map(async (deckFlashcard) => {
          const cardData = await Flashcard.findById(deckFlashcard.flashcardId);
          return {
            flashcard: {
              id: cardData._id,
              ...cardData.toObject()
            },
            sortOrder: deckFlashcard.sortOrder
          };
        })
      );
      
      return DeckAggregate.fromData({
        id: deck._id,
        name: deck.name,
        description: deck.description,
        flashcards: flashcardsWithData,
        ownerId: deck.ownerId
      });
    } catch (error) {
      await session.abortTransaction();
      throw new Error(`Error removing flashcard from deck: ${error.message}`);
    } finally {
      session.endSession();
    }
  }

  /**
   * Updates flashcard sort order within a deck.
   * @param {string} deckId - The ID of the deck.
   * @param {string} flashcardId - The ID of the flashcard.
   * @param {number} newSortOrder - The new sort order.
   * @returns {Promise<DeckAggregate>} - The updated deck aggregate.
   */
  static async updateFlashcardSortOrder({deckId, flashcardId, newSortOrder}) {
    try {
      const deck = await Deck.findById(deckId);
      if (!deck) {
        throw new NotFoundError('Deck not found.');
      }
      
      if (newSortOrder < 0 || newSortOrder >= deck.flashcards.length) {
        throw new ValidationError('Invalid sort order.');
      }
      
      // Find the flashcard in the deck
      const flashcardIndex = deck.flashcards.findIndex(
        f => f.flashcardId.toString() === flashcardId
      );
      
      if (flashcardIndex === -1) {
        throw new NotFoundError('Flashcard not found in this deck.');
      }
      
      const currentSortOrder = deck.flashcards[flashcardIndex].sortOrder;
      
      // No change needed if the sort order is the same
      if (currentSortOrder === newSortOrder) {
        // Return the current state
        const flashcardsWithData = await Promise.all(
          deck.flashcards.map(async (deckFlashcard) => {
            const cardData = await Flashcard.findById(deckFlashcard.flashcardId);
            return {
              flashcard: {
                id: cardData._id,
                ...cardData.toObject()
              },
              sortOrder: deckFlashcard.sortOrder
            };
          })
        );
        
        return DeckAggregate.fromData({
          id: deck._id,
          name: deck.name,
          description: deck.description,
          flashcards: flashcardsWithData,
          ownerId: deck.ownerId
        });
      }
      
      // Update sort orders
      if (currentSortOrder < newSortOrder) {
        // Moving down in the list
        for (const f of deck.flashcards) {
          if (f.sortOrder > currentSortOrder && f.sortOrder <= newSortOrder) {
            f.sortOrder -= 1;
          }
        }
      } else {
        // Moving up in the list
        for (const f of deck.flashcards) {
          if (f.sortOrder >= newSortOrder && f.sortOrder < currentSortOrder) {
            f.sortOrder += 1;
          }
        }
      }
      
      // Set the new sort order for the target flashcard
      deck.flashcards[flashcardIndex].sortOrder = newSortOrder;
      
      await deck.save();
      
      // Get all flashcards with their full data
      const flashcardsWithData = await Promise.all(
        deck.flashcards.map(async (deckFlashcard) => {
          const cardData = await Flashcard.findById(deckFlashcard.flashcardId);
          return {
            flashcard: {
              id: cardData._id,
              ...cardData.toObject()
            },
            sortOrder: deckFlashcard.sortOrder
          };
        })
      );
      
      return DeckAggregate.fromData({
        id: deck._id,
        name: deck.name,
        description: deck.description,
        flashcards: flashcardsWithData,
        ownerId: deck.ownerId
      });
    } catch (error) {
      throw new Error(`Error updating flashcard sort order: ${error.message}`);
    }
  }

  /**
   * Finds a deck by ID.
   * @param {string} deckId - The ID of the deck to find.
   * @param {string} userId - The ID of the user requesting the deck.
   * @returns {Promise<DeckAggregate>} - The found deck aggregate.
   */
  static async findById({deckId, userId, isPopulated = false, isWithStudentStatistics = false}) {
    try {
      console.log({deckId, userId, isPopulated, isWithStudentStatistics})
      const deck = isPopulated ? await Deck.findById(deckId).populate('flashcards.flashcardId') : await Deck.findById(deckId)
      console.log("deck debug", JSON.stringify(deck,null,2))
      
      if (!deck) {
        throw new NotFoundError('Deck not found.');
      }
      
      let flashcards = isPopulated ? deck.flashcards.map(f => { return {flashcard: f.flashcardId, sortOrder: f.sortOrder}}) : deck.flashcards
      console.log("flashcards debug", JSON.stringify(flashcards,null,2))
      const boo = await FlashcardStatistics.find({flashcardId: {$in: flashcards.map(f => f.flashcard._id)}, userId: userId})
      console.log("boo debug", JSON.stringify(boo,null,2))
      const studentStatistics = isWithStudentStatistics && isPopulated ? await FlashcardStatistics.find({flashcardId: {$in: flashcards.map(f => f.flashcard._id)}, userId: userId}) : null
      console.log("studentStatistics debug", JSON.stringify(studentStatistics,null,2))
      flashcards = flashcards.map(f => {
        if(!studentStatistics) {
          return {
            ...f,
            flashcard: {
              ...f.flashcard.toObject(),
              id: f.flashcard._id
            }
          };
        }
        const flashcardStatistics = studentStatistics.find(s => s.flashcardId.toString() === f.flashcard._id.toString())
        return {
          ...f,
          flashcard: {
            ...f.flashcard.toObject(),
            id: f.flashcard._id,
            statistics: flashcardStatistics
          }
        }
      })
      console.log("flashcards debug", JSON.stringify(flashcards,null,2))
      
      return DeckAggregate.fromData({
        id: deck._id.toString(),
        name: deck.name,
        description: deck.description,
        flashcards: flashcards,
        ownerId: deck.ownerId
      });
    } catch (error) {
      throw new Error(`Error finding deck: ${error.message}`);
    }
  }

  /**
   * Retrieves all decks with optional filtering.
   * @param {Object} filter - The filter criteria.
   * @param {string} userId - The ID of the user requesting the decks.
   * @returns {Promise<DeckAggregate[]>} - Array of deck aggregates.
   */
  static async findAll({filter = {}, userId, limit = 10, page = 0, withoutFlashcards = false}) {
    try {
      const skip = page * limit;
      const decks = withoutFlashcards ? await Deck.find(filter).skip(skip).limit(limit) : await Deck.find(filter).populate('flashcards.flashcardId').skip(skip).limit(limit);
      
      const deckAggregates = await Promise.all(
        decks.map(async (deck) => {
          return DeckAggregate.fromData({
            id: deck._id,
            name: deck.name,
            description: deck.description,
            flashcards: withoutFlashcards ? [] : deck.flashcards.map(f => { return {flashcard: f.flashcardId, sortOrder: f.sortOrder}}),
            ownerId: deck.ownerId
          });
        })
      );
      
      return deckAggregates;
    } catch (error) {
      throw new Error(`Error retrieving decks: ${error.message}`);
    }
  }

  /**
   * Removes multiple flashcards from all decks.
   * @param {string[]} flashcardIds - Array of flashcard IDs to remove.
   * @param {Object} session - MongoDB session for transaction.
   * @returns {Promise<boolean>} - True if removal was successful.
   */
  static async removeFlashcardsFromAllDecks({flashcardIds, session}) {
    try {
      // First, find all decks that contain these flashcards
      const decksWithFlashcards = await Deck.find(
        { 'flashcards.flashcardId': { $in: flashcardIds } },
        { _id: 1, 'flashcards.flashcardId': 1, 'statistics.numberOfFlashcards': 1 },
        { session }
      );

      // Process each deck individually to handle null numberOfFlashcards
      for (const deck of decksWithFlashcards) {
        // Count how many flashcards from the list are in this deck
        const flashcardsToRemove = deck.flashcards.filter(deckFlashcard => 
          flashcardIds.includes(deckFlashcard.flashcardId.toString())
        );
        const countToRemove = flashcardsToRemove.length;

        if (countToRemove > 0) {
          // Remove the flashcards
          await Deck.updateOne(
            { _id: deck._id },
            { 
              $pull: { flashcards: { flashcardId: { $in: flashcardIds } } }
            },
            { session }
          );

          // Update statistics - handle null numberOfFlashcards
          const currentCount = deck.statistics?.numberOfFlashcards || 0;
          const newCount = Math.max(0, currentCount - countToRemove);
          
          await Deck.updateOne(
            { _id: deck._id },
            { 
              $set: { 'statistics.numberOfFlashcards': newCount }
            },
            { session }
          );
        }
      }
      
      return true;
    } catch (error) {
      throw new Error(`Error removing flashcards from decks: ${error.message}`);
    }
  }

  /**
   * Deletes a deck.
   * @param {string} deckId - The ID of the deck to delete.
   * @returns {Promise<boolean>} - True if deletion was successful.
   */
  static async delete({deckId}) {
    try {
      const result = await Deck.deleteOne({ _id: deckId });
      
      if (result.deletedCount === 0) {
        throw new NotFoundError('Deck not found.');
      }
      
      return true;
    } catch (error) {
      throw new Error(`Error deleting deck: ${error.message}`);
    }
  }
}

export default DeckRepository; 