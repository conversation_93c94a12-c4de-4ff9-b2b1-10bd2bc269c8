import mongoose from 'mongoose';
import { FlashcardAggregate } from '../aggregate/flashcardAggregate.js';
import Flashcard from '../model/flashcardSchema.js';
import FlashcardStatistics from '../model/flashcardStatisticsSchema.js';
import { NotFoundError, ResourceAlreadyExistsError, ValidationError } from '../../../utils/customErrors/index.js';

class FlashcardRepository {
  /**
   * Creates a new flashcard in the database.
   * @param {Object} flashcardData - The data for the new flashcard.
   * @returns {Promise<FlashcardAggregate>} - The created flashcard aggregate.
   */
  static async create({flashcardData, session}) {
    
      // Create flashcard
      const flashcard = session ? await (new Flashcard(flashcardData)).save({session}) : await (new Flashcard(flashcardData)).save();

      // Create initial statistics
      const statistics = session ? await (new FlashcardStatistics({
        flashcardId: flashcard._id,
        userId: flashcardData.ownerId,
        ownerId: flashcardData.ownerId,
        numberOfCorrectAnswers: 0,
        numberOfIncorrectAnswers: 0,
        numberOfTimesAnswered: 0
      })).save({session}) : await (new FlashcardStatistics({
        flashcardId: flashcard._id,
        userId: flashcardData.ownerId,
        ownerId: flashcardData.ownerId,
        numberOfCorrectAnswers: 0,
        numberOfIncorrectAnswers: 0,
        numberOfTimesAnswered: 0
      })).save();
      
      return FlashcardAggregate.fromData({
        id: flashcard._id,
        ...flashcard.toObject(),
        statistics: statistics.toObject()
      });
  }

  /**
   * Updates an existing flashcard.
   * @param {string} flashcardId - The ID of the flashcard to update.
   * @param {Object} updateData - The data to update in the flashcard.
   * @returns {Promise<FlashcardAggregate>} - The updated flashcard aggregate.
   */
  static async update({flashcardId, updateData}) {
    try {
      const flashcard = await Flashcard.findById(flashcardId);
      if (!flashcard) {
        throw new NotFoundError('Flashcard not found.');
      }

      Object.assign(flashcard, updateData);
      await flashcard.save();
      
      const statistics = await FlashcardStatistics.findOne({ 
        flashcardId: flashcard._id,
        userId: flashcard.ownerId 
      });
      
      return FlashcardAggregate.fromData({
        id: flashcard._id,
        ...flashcard.toObject(),
        statistics: statistics.toObject()
      });
    } catch (error) {
      throw new Error(`Error updating flashcard: ${error.message}`);
    }
  }

  /**
   * Updates flashcard statistics.
   * @param {string} flashcardId - The ID of the flashcard.
   * @param {string} userId - The ID of the user.
   * @param {boolean} isCorrect - Whether the answer was correct.
   * @returns {Promise<FlashcardAggregate>} - The updated flashcard aggregate.
   */
  static async updateStatistics({flashcardId, userId, isCorrect}) {
    try {
      const flashcard = await Flashcard.findById(flashcardId);
      if (!flashcard) {
        throw new NotFoundError('Flashcard not found.');
      }
      
      let statistics = await FlashcardStatistics.findOne({ 
        flashcardId, 
        userId 
      });
      
      if (!statistics) {
        statistics = new FlashcardStatistics({
          flashcardId,
          userId,
          ownerId: flashcard.ownerId,
          numberOfCorrectAnswers: 0,
          numberOfIncorrectAnswers: 0,
          numberOfTimesAnswered: 0
        });
      }
      
      if (isCorrect) {
        statistics.numberOfCorrectAnswers += 1;
      } else {
        statistics.numberOfIncorrectAnswers += 1;
      }
      
      statistics.numberOfTimesAnswered += 1;
      await statistics.save();
      
      // Update the flashcard's overall statistics
      if (userId === flashcard.ownerId) {
        if (isCorrect) {
          flashcard.statistics.numberOfCorrectAnswers += 1;
        } else {
          flashcard.statistics.numberOfIncorrectAnswers += 1;
        }
        await flashcard.save();
      }
      
      return FlashcardAggregate.fromData({
        id: flashcard._id,
        ...flashcard.toObject(),
        statistics: statistics.toObject()
      });
    } catch (error) {
      throw new Error(`Error updating flashcard statistics: ${error.message}`);
    }
  }

  static async batchUpdateStatistics({answers, session}) {
    const flashcards = await Flashcard.find({_id: {$in: answers.map(answer => answer.flashcardId)}});
    const statistics = await FlashcardStatistics.find({flashcardId: {$in: flashcards.map(flashcard => flashcard._id)}});

    for (const answer of answers) {
      const flashcard = flashcards.find(flashcard => flashcard._id.equals(answer.flashcardId));
      const statistic = statistics.find(statistic => statistic.flashcardId.equals(answer.flashcardId));
      if (!flashcard || !statistic) {
        throw new NotFoundError('Flashcard or statistic not found.');
      }

      if (answer.isCorrect) {
        statistic.numberOfCorrectAnswers += 1;
      } else {
        statistic.numberOfIncorrectAnswers += 1;
      }
      statistic.numberOfTimesAnswered += 1;
      session ? await statistic.save({session}) : await statistic.save();
    }

    return true;
  }

  /**
   * Finds a flashcard by ID.
   * @param {string} flashcardId - The ID of the flashcard to find.
   * @param {string} userId - The ID of the user requesting the flashcard.
   * @returns {Promise<FlashcardAggregate>} - The found flashcard aggregate.
   */
  static async findById({flashcardId, userId}) {
    try {
      const flashcard = await Flashcard.findById(flashcardId);
      if (!flashcard) {
        throw new NotFoundError('Flashcard not found.');
      }

      const statistics = await FlashcardStatistics.findOne({ 
        flashcardId: flashcard._id,
        userId 
      }) || { 
        numberOfCorrectAnswers: 0,
        numberOfIncorrectAnswers: 0,
        numberOfTimesAnswered: 0
      };
      
      return FlashcardAggregate.fromData({
        id: flashcard._id,
        ...flashcard.toObject(),
        statistics: statistics.toObject ? statistics.toObject() : statistics
      });
    } catch (error) {
      throw new Error(`Error finding flashcard: ${error.message}`);
    }
  }

  /**
   * Retrieves all flashcards with optional filtering.
   * @param {Object} filter - The filter criteria.
   * @param {string} userId - The ID of the user requesting the flashcards.
   * @returns {Promise<FlashcardAggregate[]>} - Array of flashcard aggregates.
   */
  static async findAll({filter = {}, userId, limit = 10, page = 0,getAll = false}) {
    try {
      console.log({filter, userId})
      const skip = page * limit;
      const {ids, ...rest} = filter || {};
      let parsedFilter = {};
      if(ids){
        parsedFilter._id = {$in: ids}
      }
      if(Object.keys(rest).length > 0){
        parsedFilter = {...parsedFilter, ...rest}
      }
      // Add userId filter if provided
      if(userId){
        parsedFilter.ownerId = userId;
      }
      const flashcards = getAll ? await Flashcard.find(parsedFilter) : await Flashcard.find(parsedFilter).limit(limit).skip(skip);
      console.log({flashcards})

      const flashcardAggregates = flashcards.map(flashcard => {
        return FlashcardAggregate.fromData({
          id: flashcard._id.toString(),
          ...flashcard.toObject(),
          statistics: flashcard.statistics ? flashcard.statistics.toObject() : null
        });
      })
      return flashcardAggregates;
    } catch (error) {
      console.log({error})
      throw new Error(`Error retrieving flashcards: ${error.message}`);
    }
  }

  /**
   * Deletes a flashcard.
   * @param {string} flashcardId - The ID of the flashcard to delete.
   * @returns {Promise<boolean>} - True if deletion was successful.
   */
  static async delete({flashcardId}) {
    const session = await mongoose.startSession();
    session.startTransaction();
    
    try {
      // Delete the flashcard
      const result = await Flashcard.deleteOne({ _id: flashcardId }).session(session);
      
      if (result.deletedCount === 0) {
        throw new NotFoundError('Flashcard not found.');
      }
      
      // Delete all statistics records associated with this flashcard
      await FlashcardStatistics.deleteMany({ flashcardId }).session(session);
      
      await session.commitTransaction();
      return true;
    } catch (error) {
      await session.abortTransaction();
      throw new Error(`Error deleting flashcard: ${error.message}`);
    } finally {
      session.endSession();
    }
  }

  /**
   * Deletes multiple flashcards.
   * @param {string[]} flashcardIds - Array of flashcard IDs to delete.
   * @param {Object} session - MongoDB session for transaction.
   * @returns {Promise<boolean>} - True if deletion was successful.
   */
  static async deleteMultiple({flashcardIds, session}) {
    try {
      // Delete the flashcards
      const result = await Flashcard.deleteMany({ _id: { $in: flashcardIds } }).session(session);
      
      if (result.deletedCount === 0) {
        throw new NotFoundError('No flashcards found to delete.');
      }
      
      // Delete all statistics records associated with these flashcards
      await FlashcardStatistics.deleteMany({ flashcardId: { $in: flashcardIds } }).session(session);
      
      return true;
    } catch (error) {
      throw new Error(`Error deleting flashcards: ${error.message}`);
    }
  }
}

export default FlashcardRepository; 