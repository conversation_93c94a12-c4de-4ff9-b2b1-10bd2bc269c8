import { GroupModel } from "../schema/groupSchema.js";

export const fetchUserGroups = async ({ userId, page = 1, limit = 10 , isWithoutPagination = false}) => {
  const skip = (page - 1) * limit;

  // Always filter by user membership - never return all groups
  const membershipFilter = { members: userId };

  const [groups, total] = await Promise.all([
    isWithoutPagination
      ? GroupModel.find(membershipFilter)
      : GroupModel.find(membershipFilter).skip(skip).limit(limit),
    GroupModel.countDocuments(membershipFilter),
  ]);

  const totalPages = Math.ceil(total / limit);

  console.log(`fetchUserGroups: Found ${groups.length} groups for user ${userId} (total: ${total})`);

  return {
    data: groups,
    pagination: {
      total,
      page,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    },
  };
};
