import { sendMessageToMatrixisQueue } from '../../../utils/aws/sqs.js';

class MatrixisHandler {
    static createMessageParams(eventType, data) {
        return {
            Message: JSON.stringify({
                eventType,
                data
            }),
        };
    }

    static async publishPathXP({  userId, pathId, totalXp, partialXp }) {
        // Create message payload
        const messagePayload = {
            eventType: 'Path_Completed',
            data: {  userId, pathId, totalXp, partialXp }
        };
        
        console.log("Publishing to Matrixis");
        
        try {
            // Try SQS first
            await sendMessageToMatrixisQueue("message-handler-queue", messagePayload);
            console.log(`Message sent to Matrixis queue successfully`);
        } catch (sqsError) {
            console.error('Error publishing message to SQS:', sqsError);
        }
    }

    static async makeShopTransaction({ userId, itemId, amount, buyId, shopId, quantity }) {
        const messagePayload = {
            eventType: 'Make_Shop_Transaction',
            data: { userId, itemId, amount, buyId, shopId, quantity }
        };
        
        console.log("Publishing to Matrixis");
        
        try {
            await sendMessageToMatrixisQueue("message-handler-queue", messagePayload);
            console.log(`Message sent to Matrixis queue successfully`);
        } catch (sqsError) {
            console.error('Error publishing message to SQS:', sqsError);
        }
    }

    static async publishLoginStreakXP({ userId, streak }){
        // Create message payload
        const messagePayload = {
            eventType: 'Login_Streak',
            data: { userId }
        };
        
        console.log("Publishing to Matrixis");
        
        try {
            // Try SQS first
            await sendMessageToMatrixisQueue("message-handler-queue", messagePayload);
            console.log(`Message sent to Matrixis queue successfully`);
        } catch (sqsError) {
            console.error('Error publishing message to SQS:', sqsError);
        }
    }
}

export default MatrixisHandler;