import UserAggregateRoot from "../../../domain/aggregates/user/userAggregateRoot.js";
import UserRepository from "../../../domain/repositories/userRepository.js";

const userRepository = new UserRepository();
async function updateUserService({ id, updateData }) {
    const userAggregate = await userRepository.findById(id);
    const updateDataInfo = UserAggregateRoot.fromData({...updateData})

    const updatedUser = await userRepository.update({ user: userAggregate, updateData: updateDataInfo });

    return updatedUser;
}

export default updateUserService;