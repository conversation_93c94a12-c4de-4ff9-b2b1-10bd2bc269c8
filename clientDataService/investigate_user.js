#!/usr/bin/env node

// Script para verificar associações do usuário com grupos
import { UserModel } from './src/domain/models/userModel.js';
import { GroupModel } from './src/modules/groups/schema/groupSchema.js';
import { LessonMetricModel } from './src/domain/models/metrics/lessonMetricsModel.js';
import connectToDB from './src/infra/libs/mongodb/connect.js';

async function investigateUser() {
    try {
        await connectToDB();
        console.log('Conectado ao MongoDB');

        const userId = '68b61502bf77c9e762f32da6';
        console.log(`\n=== Investigando usuário ${userId} ===`);

        // 1. Buscar informações do usuário
        const user = await UserModel.findById(userId);
        if (!user) {
            console.log('❌ Usuário não encontrado');
            return;
        }
        
        console.log('\n📋 INFORMAÇÕES DO USUÁRIO:');
        console.log(`Nome: ${user.name}`);
        console.log(`Email: ${user.email}`);
        console.log(`Role: ${user.role}`);
        console.log(`School ID: ${user.schoolId}`);
        console.log(`Contract ID: ${user.contractId}`);

        // 2. Buscar grupos onde o usuário é membro
        const userGroups = await GroupModel.find({ members: userId });
        console.log(`\n👥 GRUPOS DO USUÁRIO (${userGroups.length}):`);
        
        userGroups.forEach((group, index) => {
            console.log(`${index + 1}. ${group.name} (${group.category}) - ID: ${group._id}`);
            console.log(`   Membros: ${group.members.length}`);
            if (group.parentGroupId) {
                console.log(`   Grupo pai: ${group.parentGroupId}`);
            }
        });

        // 3. Buscar métricas de lição para o comentário específico
        const commentId = 43;
        const videoId = 234;
        
        console.log(`\n🎯 PROCURANDO COMENTÁRIO ${commentId} DO VÍDEO ${videoId}:`);
        
        const lessonMetrics = await LessonMetricModel.find({
            userId: userId,
            videoId: videoId,
            $or: [
                { comments: commentId },
                { 'doubts.commentId': commentId }
            ]
        });

        if (lessonMetrics.length === 0) {
            console.log('❌ Nenhuma métrica encontrada para este comentário');
        } else {
            lessonMetrics.forEach((metric, index) => {
                console.log(`\n📊 MÉTRICA ${index + 1}:`);
                console.log(`   Video ID: ${metric.videoId}`);
                console.log(`   Playlist ID: ${metric.playlistId}`);
                console.log(`   Comments: [${metric.comments.join(', ')}]`);
                console.log(`   Doubts: ${JSON.stringify(metric.doubts, null, 2)}`);
            });
        }

        // 4. Buscar todas as métricas do usuário para ver padrões
        const allUserMetrics = await LessonMetricModel.find({ userId: userId });
        console.log(`\n📈 TOTAL DE MÉTRICAS DO USUÁRIO: ${allUserMetrics.length}`);
        
        const playlistIds = [...new Set(allUserMetrics.map(m => m.playlistId).filter(Boolean))];
        console.log(`🎵 PLAYLISTS ASSOCIADAS: [${playlistIds.join(', ')}]`);

        // 5. Para cada grupo, verificar se há métricas que poderiam mostrar comentários
        for (const group of userGroups) {
            console.log(`\n🔍 INVESTIGANDO GRUPO: ${group.name} (${group._id})`);
            
            // Buscar todos os membros do grupo
            const groupMembers = await UserModel.find({ _id: { $in: group.members } });
            console.log(`   Membros no grupo: ${groupMembers.length}`);
            
            // Buscar métricas com comentários de todos os membros do grupo
            const groupMetricsWithComments = await LessonMetricModel.find({
                userId: { $in: group.members },
                $or: [
                    { comments: { $exists: true, $not: { $size: 0 } } },
                    { doubts: { $exists: true, $not: { $size: 0 } } }
                ]
            });
            
            console.log(`   Métricas com comentários no grupo: ${groupMetricsWithComments.length}`);
            
            // Procurar especificamente pelo nosso comentário
            const ourCommentInGroup = await LessonMetricModel.find({
                userId: { $in: group.members },
                $or: [
                    { comments: commentId },
                    { 'doubts.commentId': commentId }
                ]
            });
            
            if (ourCommentInGroup.length > 0) {
                console.log(`   ✅ COMENTÁRIO ${commentId} ENCONTRADO NESTE GRUPO!`);
                ourCommentInGroup.forEach(metric => {
                    console.log(`      User: ${metric.userId}, Video: ${metric.videoId}, Playlist: ${metric.playlistId}`);
                });
            } else {
                console.log(`   ❌ Comentário ${commentId} NÃO encontrado neste grupo`);
            }
        }

    } catch (error) {
        console.error('Erro:', error);
    } finally {
        process.exit(0);
    }
}

investigateUser();