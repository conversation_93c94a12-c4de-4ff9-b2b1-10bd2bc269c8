#!/usr/bin/env node

/**
 * Database Index Optimization Script
 * 
 * This script creates optimized indexes for the LessonMetrics collection
 * to improve query performance for comment retrieval operations.
 * 
 * Usage:
 *   node scripts/optimizeIndexes.js [--dry-run] [--force]
 * 
 * Options:
 *   --dry-run  Show what indexes would be created without making changes
 *   --force    Drop existing indexes before creating new ones
 */

import mongoose from 'mongoose';
import { LessonMetricModel } from '../src/domain/models/metrics/lessonMetricsModel.js';

// Command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const forceRecreate = args.includes('--force');

// Index definitions for optimal query performance
const OPTIMIZED_INDEXES = [
    {
        name: 'userId_videoId_playlistId_unique',
        fields: { userId: 1, videoId: 1, playlistId: 1 },
        options: { unique: true, background: true },
        description: 'Unique compound index for user-video-playlist combinations'
    },
    {
        name: 'userId_doubts_sparse',
        fields: { userId: 1, 'doubts.commentId': 1, 'doubts.status': 1 },
        options: { sparse: true, background: true },
        description: 'Optimized index for doubt queries by user'
    },
    {
        name: 'videoId_doubts_compound',
        fields: { videoId: 1, 'doubts.commentId': 1 },
        options: { sparse: true, background: true },
        description: 'Compound index for video-specific doubt queries'
    },
    {
        name: 'userId_comments_sparse',
        fields: { userId: 1, comments: 1 },
        options: { sparse: true, background: true },
        description: 'Optimized index for comment queries by user'
    },
    {
        name: 'createdAt_desc',
        fields: { createdAt: -1 },
        options: { background: true },
        description: 'Descending index for chronological queries'
    },
    {
        name: 'videoId_userId_compound',
        fields: { videoId: 1, userId: 1 },
        options: { background: true },
        description: 'Compound index for video-user queries'
    },
    {
        name: 'playlistId_userId_compound',
        fields: { playlistId: 1, userId: 1 },
        options: { sparse: true, background: true },
        description: 'Compound index for playlist-user queries'
    }
];

/**
 * Connect to MongoDB
 */
async function connectToDatabase() {
    try {
        const mongoUri = process.env.CONNECTION_STRING || process.env.MONGODB_URI || 'mongodb://localhost:27017/atomize-dev';
        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        console.log(`📍 Using database: ${mongoose.connection.name}`);
    } catch (error) {
        console.error('❌ Failed to connect to MongoDB:', error.message);
        process.exit(1);
    }
}

/**
 * Get existing indexes
 */
async function getExistingIndexes() {
    try {
        const indexes = await LessonMetricModel.collection.getIndexes();
        return indexes;
    } catch (error) {
        console.error('❌ Failed to get existing indexes:', error.message);
        return {};
    }
}

/**
 * Check if an index exists
 */
function indexExists(existingIndexes, indexName) {
    return existingIndexes.hasOwnProperty(indexName);
}

/**
 * Create a single index
 */
async function createIndex(indexDef) {
    try {
        if (isDryRun) {
            console.log(`🔍 [DRY RUN] Would create index: ${indexDef.name}`);
            console.log(`   Fields: ${JSON.stringify(indexDef.fields)}`);
            console.log(`   Options: ${JSON.stringify(indexDef.options)}`);
            return true;
        }

        console.log(`🔧 Creating index: ${indexDef.name}...`);
        await LessonMetricModel.collection.createIndex(indexDef.fields, {
            ...indexDef.options,
            name: indexDef.name
        });
        console.log(`✅ Successfully created index: ${indexDef.name}`);
        return true;
    } catch (error) {
        if (error.code === 11000 || error.message.includes('already exists')) {
            console.log(`ℹ️  Index ${indexDef.name} already exists, skipping`);
            return true;
        }
        console.error(`❌ Failed to create index ${indexDef.name}:`, error.message);
        return false;
    }
}

/**
 * Drop an index
 */
async function dropIndex(indexName) {
    try {
        if (isDryRun) {
            console.log(`🔍 [DRY RUN] Would drop index: ${indexName}`);
            return true;
        }

        console.log(`🗑️  Dropping index: ${indexName}...`);
        await LessonMetricModel.collection.dropIndex(indexName);
        console.log(`✅ Successfully dropped index: ${indexName}`);
        return true;
    } catch (error) {
        if (error.message.includes('index not found')) {
            console.log(`ℹ️  Index ${indexName} not found, skipping drop`);
            return true;
        }
        console.error(`❌ Failed to drop index ${indexName}:`, error.message);
        return false;
    }
}

/**
 * Analyze query performance
 */
async function analyzeQueryPerformance() {
    console.log('\n📊 Analyzing query performance...');
    
    const sampleQueries = [
        {
            name: 'Find user doubts',
            query: { userId: new mongoose.Types.ObjectId(), 'doubts.0': { $exists: true } },
            description: 'Query for user-specific doubts'
        },
        {
            name: 'Find video comments',
            query: { videoId: 123, 'comments.0': { $exists: true } },
            description: 'Query for video-specific comments'
        },
        {
            name: 'Find recent metrics',
            query: { createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } },
            description: 'Query for recent lesson metrics'
        }
    ];

    for (const queryTest of sampleQueries) {
        try {
            const explain = await LessonMetricModel.collection.find(queryTest.query).explain('executionStats');
            const stats = explain.executionStats;
            
            console.log(`\n🔍 ${queryTest.name}:`);
            console.log(`   Description: ${queryTest.description}`);
            console.log(`   Documents examined: ${stats.totalDocsExamined}`);
            console.log(`   Documents returned: ${stats.totalDocsReturned}`);
            console.log(`   Execution time: ${stats.executionTimeMillis}ms`);
            console.log(`   Index used: ${stats.winningPlan?.inputStage?.indexName || 'COLLSCAN'}`);
            
            if (stats.totalDocsExamined > stats.totalDocsReturned * 10) {
                console.log(`   ⚠️  Warning: High document examination ratio (${stats.totalDocsExamined}/${stats.totalDocsReturned})`);
            }
        } catch (error) {
            console.warn(`   ⚠️  Could not analyze query: ${error.message}`);
        }
    }
}

/**
 * Display index statistics
 */
async function displayIndexStats() {
    try {
        const stats = await LessonMetricModel.collection.stats();
        const indexes = await LessonMetricModel.collection.getIndexes();
        
        console.log('\n📈 Collection Statistics:');
        console.log(`   Total documents: ${stats.count}`);
        console.log(`   Average document size: ${Math.round(stats.avgObjSize)} bytes`);
        console.log(`   Total collection size: ${Math.round(stats.size / 1024 / 1024)} MB`);
        console.log(`   Total index size: ${Math.round(stats.totalIndexSize / 1024 / 1024)} MB`);
        
        console.log('\n📋 Existing Indexes:');
        Object.keys(indexes).forEach(indexName => {
            const index = indexes[indexName];
            console.log(`   ${indexName}: ${JSON.stringify(index)}`);
        });
    } catch (error) {
        console.warn('⚠️  Could not retrieve collection statistics:', error.message);
    }
}

/**
 * Main execution function
 */
async function main() {
    try {
        console.log(`🔧 Starting database index optimization (${isDryRun ? 'DRY RUN' : 'LIVE MODE'})...`);
        
        await connectToDatabase();
        
        // Display current statistics
        await displayIndexStats();
        
        // Get existing indexes
        const existingIndexes = await getExistingIndexes();
        console.log(`\n📋 Found ${Object.keys(existingIndexes).length} existing indexes`);
        
        let successCount = 0;
        let skipCount = 0;
        let errorCount = 0;
        
        // Process each optimized index
        for (const indexDef of OPTIMIZED_INDEXES) {
            console.log(`\n🔍 Processing index: ${indexDef.name}`);
            console.log(`   Description: ${indexDef.description}`);
            
            // Check if index exists and force recreation is requested
            if (forceRecreate && indexExists(existingIndexes, indexDef.name)) {
                const dropped = await dropIndex(indexDef.name);
                if (!dropped) {
                    errorCount++;
                    continue;
                }
            }
            
            // Create the index
            const created = await createIndex(indexDef);
            if (created) {
                if (indexExists(existingIndexes, indexDef.name) && !forceRecreate) {
                    skipCount++;
                } else {
                    successCount++;
                }
            } else {
                errorCount++;
            }
        }
        
        // Analyze query performance after optimization
        if (!isDryRun) {
            await analyzeQueryPerformance();
        }
        
        // Display final results
        console.log('\n' + '='.repeat(60));
        console.log('📊 INDEX OPTIMIZATION RESULTS');
        console.log('='.repeat(60));
        console.log(`Indexes processed: ${OPTIMIZED_INDEXES.length}`);
        console.log(`Successfully created: ${successCount}`);
        console.log(`Skipped (already exist): ${skipCount}`);
        console.log(`Errors: ${errorCount}`);
        
        if (isDryRun) {
            console.log('\n🔍 This was a dry run. Use without --dry-run to apply changes.');
        }
        
        console.log('\n✅ Database index optimization completed!');
        process.exit(0);
        
    } catch (error) {
        console.error('\n❌ Database index optimization failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
    }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { OPTIMIZED_INDEXES, createIndex, analyzeQueryPerformance };
