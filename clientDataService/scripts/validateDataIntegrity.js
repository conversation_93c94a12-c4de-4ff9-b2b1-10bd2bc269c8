#!/usr/bin/env node

/**
 * Data Integrity Validation Script for LessonMetrics
 * 
 * This script validates and fixes data integrity issues in the LessonMetrics collection:
 * 1. Checks for records with missing or null videoId values
 * 2. Validates comment IDs in comments and doubts arrays
 * 3. Checks for orphaned comment references
 * 4. Provides options to fix identified issues
 * 
 * Usage:
 *   node scripts/validateDataIntegrity.js [--fix] [--dry-run]
 * 
 * Options:
 *   --fix      Apply fixes to identified issues
 *   --dry-run  Show what would be fixed without making changes (default)
 */

import mongoose from 'mongoose';
import { LessonMetricModel } from '../src/domain/models/metrics/lessonMetricsModel.js';
import SapsApi from '../src/infra/api/SapsApi.js';

// Configuration
const BATCH_SIZE = 100;
const MAX_RETRIES = 3;

// Command line arguments
const args = process.argv.slice(2);
const shouldFix = args.includes('--fix');
const isDryRun = !shouldFix || args.includes('--dry-run');

// Statistics tracking
const stats = {
    totalRecords: 0,
    recordsWithNullVideoId: 0,
    recordsWithInvalidComments: 0,
    recordsWithOrphanedComments: 0,
    recordsFixed: 0,
    errors: []
};

/**
 * Connect to MongoDB
 */
async function connectToDatabase() {
    try {
        // Use the same connection string as the CDS service
        const mongoUri = process.env.CONNECTION_STRING || process.env.MONGODB_URI || 'mongodb://localhost:27017/atomize-dev';
        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        console.log(`📍 Using database: ${mongoose.connection.name}`);
    } catch (error) {
        console.error('❌ Failed to connect to MongoDB:', error.message);
        console.error('💡 Make sure CONNECTION_STRING environment variable is set');
        process.exit(1);
    }
}

/**
 * Validate a single LessonMetrics record
 */
async function validateRecord(record) {
    const issues = [];
    
    // Check for null or missing videoId
    if (!record.videoId || record.videoId === null) {
        issues.push({
            type: 'NULL_VIDEO_ID',
            message: `Record ${record._id} has null/missing videoId`,
            severity: 'HIGH'
        });
    }
    
    // Validate comment IDs format
    if (record.comments && record.comments.length > 0) {
        const invalidComments = record.comments.filter(commentId => {
            return !commentId || typeof commentId !== 'string' || commentId.trim() === '';
        });
        
        if (invalidComments.length > 0) {
            issues.push({
                type: 'INVALID_COMMENT_IDS',
                message: `Record ${record._id} has ${invalidComments.length} invalid comment IDs`,
                severity: 'MEDIUM',
                data: invalidComments
            });
        }
    }
    
    // Validate doubt comment IDs format
    if (record.doubts && record.doubts.length > 0) {
        const invalidDoubts = record.doubts.filter(doubt => {
            return !doubt.commentId || typeof doubt.commentId !== 'string' || doubt.commentId.trim() === '';
        });
        
        if (invalidDoubts.length > 0) {
            issues.push({
                type: 'INVALID_DOUBT_IDS',
                message: `Record ${record._id} has ${invalidDoubts.length} invalid doubt IDs`,
                severity: 'MEDIUM',
                data: invalidDoubts
            });
        }
    }
    
    return issues;
}

/**
 * Check if comment IDs exist in SAPS (with retry logic)
 */
async function validateCommentExistence(commentIds, token) {
    const orphanedComments = [];
    
    for (const commentId of commentIds) {
        let retries = 0;
        let exists = false;
        
        while (retries < MAX_RETRIES && !exists) {
            try {
                const commentInfo = await SapsApi.getCommentInfo({ commentId, token });
                if (commentInfo && commentInfo.id) {
                    exists = true;
                }
            } catch (error) {
                if (error.status === 404) {
                    // Comment doesn't exist
                    break;
                } else {
                    retries++;
                    if (retries < MAX_RETRIES) {
                        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
                    }
                }
            }
        }
        
        if (!exists) {
            orphanedComments.push(commentId);
        }
    }
    
    return orphanedComments;
}

/**
 * Fix issues in a LessonMetrics record
 */
async function fixRecord(record, issues) {
    const updates = {};
    let hasChanges = false;
    
    for (const issue of issues) {
        switch (issue.type) {
            case 'NULL_VIDEO_ID':
                // For null videoId, we can't automatically fix this
                // Log it for manual investigation
                console.warn(`⚠️  Cannot auto-fix null videoId for record ${record._id} - requires manual investigation`);
                break;
                
            case 'INVALID_COMMENT_IDS':
                // Remove invalid comment IDs
                const validComments = record.comments.filter(commentId => 
                    commentId && typeof commentId === 'string' && commentId.trim() !== ''
                );
                if (validComments.length !== record.comments.length) {
                    updates.comments = validComments;
                    hasChanges = true;
                    console.log(`🔧 Removing ${record.comments.length - validComments.length} invalid comment IDs from record ${record._id}`);
                }
                break;
                
            case 'INVALID_DOUBT_IDS':
                // Remove invalid doubt IDs
                const validDoubts = record.doubts.filter(doubt => 
                    doubt.commentId && typeof doubt.commentId === 'string' && doubt.commentId.trim() !== ''
                );
                if (validDoubts.length !== record.doubts.length) {
                    updates.doubts = validDoubts;
                    hasChanges = true;
                    console.log(`🔧 Removing ${record.doubts.length - validDoubts.length} invalid doubt IDs from record ${record._id}`);
                }
                break;
        }
    }
    
    // Apply updates if there are changes
    if (hasChanges && !isDryRun) {
        try {
            await LessonMetricModel.updateOne({ _id: record._id }, updates);
            stats.recordsFixed++;
            console.log(`✅ Fixed record ${record._id}`);
        } catch (error) {
            console.error(`❌ Failed to fix record ${record._id}:`, error.message);
            stats.errors.push(`Failed to fix record ${record._id}: ${error.message}`);
        }
    } else if (hasChanges && isDryRun) {
        console.log(`🔍 [DRY RUN] Would fix record ${record._id} with updates:`, updates);
    }
    
    return hasChanges;
}

/**
 * Process records in batches
 */
async function processRecords() {
    console.log(`🔍 Starting data integrity validation (${isDryRun ? 'DRY RUN' : 'LIVE MODE'})...`);
    
    let skip = 0;
    let hasMore = true;
    
    while (hasMore) {
        try {
            const records = await LessonMetricModel
                .find({})
                .skip(skip)
                .limit(BATCH_SIZE)
                .lean();
            
            if (records.length === 0) {
                hasMore = false;
                break;
            }
            
            console.log(`📊 Processing batch: ${skip + 1} to ${skip + records.length}`);
            
            for (const record of records) {
                stats.totalRecords++;
                
                const issues = await validateRecord(record);
                
                if (issues.length > 0) {
                    // Count issues by type
                    issues.forEach(issue => {
                        switch (issue.type) {
                            case 'NULL_VIDEO_ID':
                                stats.recordsWithNullVideoId++;
                                break;
                            case 'INVALID_COMMENT_IDS':
                            case 'INVALID_DOUBT_IDS':
                                stats.recordsWithInvalidComments++;
                                break;
                        }
                    });
                    
                    // Display issues
                    console.log(`\n⚠️  Issues found in record ${record._id}:`);
                    issues.forEach(issue => {
                        console.log(`   ${issue.severity}: ${issue.message}`);
                    });
                    
                    // Fix issues if requested
                    if (shouldFix) {
                        await fixRecord(record, issues);
                    }
                }
            }
            
            skip += BATCH_SIZE;
            
            // Progress indicator
            if (skip % (BATCH_SIZE * 10) === 0) {
                console.log(`📈 Progress: ${skip} records processed...`);
            }
            
        } catch (error) {
            console.error(`❌ Error processing batch starting at ${skip}:`, error.message);
            stats.errors.push(`Batch error at ${skip}: ${error.message}`);
            skip += BATCH_SIZE; // Continue with next batch
        }
    }
}

/**
 * Display final statistics
 */
function displayStatistics() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 DATA INTEGRITY VALIDATION RESULTS');
    console.log('='.repeat(60));
    console.log(`Total records processed: ${stats.totalRecords}`);
    console.log(`Records with null videoId: ${stats.recordsWithNullVideoId}`);
    console.log(`Records with invalid comments: ${stats.recordsWithInvalidComments}`);
    console.log(`Records with orphaned comments: ${stats.recordsWithOrphanedComments}`);
    
    if (shouldFix && !isDryRun) {
        console.log(`Records fixed: ${stats.recordsFixed}`);
    }
    
    if (stats.errors.length > 0) {
        console.log(`\n❌ Errors encountered: ${stats.errors.length}`);
        stats.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    console.log('\n' + '='.repeat(60));
    
    if (isDryRun) {
        console.log('🔍 This was a dry run. Use --fix to apply changes.');
    }
}

/**
 * Main execution function
 */
async function main() {
    try {
        await connectToDatabase();
        await processRecords();
        displayStatistics();
        
        console.log('\n✅ Data integrity validation completed successfully!');
        process.exit(0);
        
    } catch (error) {
        console.error('\n❌ Data integrity validation failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
    }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { validateRecord, fixRecord, processRecords };
