#!/usr/bin/env node

/**
 * End-to-End Validation Script for LessonMetrics and Comment System
 * 
 * This script validates the complete flow from comment creation to teacher panel display:
 * 1. Tests comment creation in SAPS with proper videoId persistence
 * 2. Validates CDS synchronization and LessonMetrics updates
 * 3. Tests comment enrichment and teacher panel data retrieval
 * 4. Validates performance optimizations and caching
 * 
 * Usage:
 *   node scripts/endToEndValidation.js [--token=TOKEN] [--video-id=ID] [--group-id=ID]
 */

import mongoose from 'mongoose';
import { LessonMetricModel } from '../src/domain/models/metrics/lessonMetricsModel.js';
import SapsApi from '../src/infra/api/SapsApi.js';
import commentCache from '../src/infra/cache/commentCache.js';
import performanceMonitor from '../src/utils/performanceMonitor.js';

// Configuration
const CONFIG = {
    SAPS_API_URL: process.env.SAPS_API_URL || 'https://ivslcxob6c.execute-api.us-east-2.amazonaws.com/dev',
    CDS_API_URL: process.env.CDS_API_URL || 'https://rxq8k76df4.execute-api.us-east-2.amazonaws.com/dev',
    TEST_USER_ID: '6730ce15f89eaab60894a8',
    TEST_VIDEO_ID: 171,
    TEST_GROUP_ID: '6789b3eb86b5ce25ca418ca9',
    TEST_PLAYLIST_ID: 41
};

// Parse command line arguments
const args = process.argv.slice(2);
const token = args.find(arg => arg.startsWith('--token='))?.split('=')[1];
const videoId = parseInt(args.find(arg => arg.startsWith('--video-id='))?.split('=')[1]) || CONFIG.TEST_VIDEO_ID;
const groupId = args.find(arg => arg.startsWith('--group-id='))?.split('=')[1] || CONFIG.TEST_GROUP_ID;

// Test results tracking
const testResults = {
    passed: 0,
    failed: 0,
    tests: []
};

/**
 * Connect to MongoDB
 */
async function connectToDatabase() {
    try {
        const mongoUri = process.env.CONNECTION_STRING || process.env.MONGODB_URI || 'mongodb://localhost:27017/atomize-dev';
        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ Failed to connect to MongoDB:', error.message);
        process.exit(1);
    }
}

/**
 * Test helper function
 */
function test(name, testFn) {
    return async () => {
        const timerId = performanceMonitor.startTimer(`test_${name.replace(/\s+/g, '_')}`);
        
        try {
            console.log(`\n🧪 Testing: ${name}`);
            await testFn();
            console.log(`✅ PASSED: ${name}`);
            testResults.passed++;
            testResults.tests.push({ name, status: 'PASSED', error: null });
        } catch (error) {
            console.error(`❌ FAILED: ${name} - ${error.message}`);
            testResults.failed++;
            testResults.tests.push({ name, status: 'FAILED', error: error.message });
        } finally {
            performanceMonitor.endTimer(timerId);
        }
    };
}

/**
 * Test 1: Comment Creation in SAPS
 */
const testCommentCreation = test('Comment Creation in SAPS', async () => {
    if (!token) {
        throw new Error('Token required for SAPS API calls');
    }

    const commentData = {
        videoId,
        userId: CONFIG.TEST_USER_ID,
        username: 'Test User',
        content: `🧪 E2E Test Comment - ${new Date().toISOString()}`,
        isDoubt: true
    };

    const response = await fetch(`${CONFIG.SAPS_API_URL}/video/comment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': token
        },
        body: JSON.stringify(commentData)
    });

    if (!response.ok) {
        throw new Error(`SAPS API returned ${response.status}: ${await response.text()}`);
    }

    const result = await response.json();
    
    if (!result.id) {
        throw new Error('Comment creation did not return an ID');
    }

    if (result.videoId !== videoId) {
        throw new Error(`VideoId mismatch: expected ${videoId}, got ${result.videoId}`);
    }

    console.log(`   Created comment ID: ${result.id} with videoId: ${result.videoId}`);
    
    // Store for later tests
    CONFIG.TEST_COMMENT_ID = result.id;
});

/**
 * Test 2: Comment Info Retrieval from SAPS
 */
const testCommentInfoRetrieval = test('Comment Info Retrieval from SAPS', async () => {
    if (!CONFIG.TEST_COMMENT_ID) {
        throw new Error('No test comment ID available');
    }

    const commentInfo = await SapsApi.getCommentInfo({
        commentId: CONFIG.TEST_COMMENT_ID,
        token
    });

    if (!commentInfo) {
        throw new Error('Failed to retrieve comment info from SAPS');
    }

    if (!commentInfo.videoId) {
        throw new Error('Comment info missing videoId');
    }

    if (commentInfo.videoId !== videoId) {
        throw new Error(`VideoId mismatch: expected ${videoId}, got ${commentInfo.videoId}`);
    }

    if (!commentInfo.video || !commentInfo.video.title) {
        throw new Error('Comment info missing video details');
    }

    console.log(`   Retrieved comment: "${commentInfo.content.substring(0, 50)}..."`);
    console.log(`   Video: ${commentInfo.video.title} (ID: ${commentInfo.videoId})`);
});

/**
 * Test 3: CDS LessonMetrics Synchronization
 */
const testCdsSync = test('CDS LessonMetrics Synchronization', async () => {
    if (!CONFIG.TEST_COMMENT_ID) {
        throw new Error('No test comment ID available');
    }

    // Wait a moment for async sync to complete
    await new Promise(resolve => setTimeout(resolve, 3000));

    const lessonMetric = await LessonMetricModel.findOne({
        userId: CONFIG.TEST_USER_ID,
        videoId: videoId
    });

    if (!lessonMetric) {
        throw new Error('LessonMetric not found in CDS');
    }

    const hasComment = lessonMetric.comments.includes(CONFIG.TEST_COMMENT_ID);
    if (!hasComment) {
        throw new Error(`Comment ${CONFIG.TEST_COMMENT_ID} not found in LessonMetrics.comments`);
    }

    const hasDoubt = lessonMetric.doubts.some(doubt => doubt.commentId === CONFIG.TEST_COMMENT_ID);
    if (!hasDoubt) {
        throw new Error(`Doubt ${CONFIG.TEST_COMMENT_ID} not found in LessonMetrics.doubts`);
    }

    console.log(`   LessonMetric found with ${lessonMetric.comments.length} comments and ${lessonMetric.doubts.length} doubts`);
});

/**
 * Test 4: Teacher Panel Data Retrieval
 */
const testTeacherPanelRetrieval = test('Teacher Panel Data Retrieval', async () => {
    const response = await fetch(`${CONFIG.CDS_API_URL}/user/comments/group/${groupId}?isDoubt=true&limit=10`, {
        headers: {
            'Authorization': token
        }
    });

    if (!response.ok) {
        throw new Error(`CDS API returned ${response.status}: ${await response.text()}`);
    }

    const result = await response.json();
    
    if (!result.comments || !Array.isArray(result.comments)) {
        throw new Error('Invalid response format from CDS');
    }

    const testComment = result.comments.find(comment => comment.id == CONFIG.TEST_COMMENT_ID);
    if (!testComment) {
        throw new Error(`Test comment ${CONFIG.TEST_COMMENT_ID} not found in teacher panel results`);
    }

    if (!testComment.video || !testComment.video.title) {
        throw new Error('Comment missing video information in teacher panel');
    }

    if (testComment.video.title.includes('undefined')) {
        throw new Error('Video title contains "undefined" - enrichment failed');
    }

    console.log(`   Found test comment in teacher panel with video: "${testComment.video.title}"`);
    console.log(`   Total doubts retrieved: ${result.comments.length}`);
});

/**
 * Test 5: Cache Performance
 */
const testCachePerformance = test('Cache Performance', async () => {
    // Clear cache to start fresh
    commentCache.clear();

    // First call should be a cache miss
    const timerId1 = performanceMonitor.startTimer('cache_miss_test');
    const commentInfo1 = await SapsApi.getCommentInfo({
        commentId: CONFIG.TEST_COMMENT_ID,
        token
    });
    const metric1 = performanceMonitor.endTimer(timerId1);

    // Second call should be a cache hit
    const timerId2 = performanceMonitor.startTimer('cache_hit_test');
    const commentInfo2 = commentCache.getCachedCommentInfo(CONFIG.TEST_COMMENT_ID);
    const metric2 = performanceMonitor.endTimer(timerId2);

    if (!commentInfo2) {
        throw new Error('Comment not found in cache after first retrieval');
    }

    if (commentInfo1.id !== commentInfo2.id) {
        throw new Error('Cached comment data does not match original');
    }

    // Cache hit should be significantly faster
    if (metric2.duration >= metric1.duration) {
        console.warn(`⚠️  Cache hit (${metric2.duration}ms) not faster than miss (${metric1.duration}ms)`);
    }

    console.log(`   Cache miss: ${metric1.duration}ms, Cache hit: ${metric2.duration}ms`);
    console.log(`   Performance improvement: ${Math.round((1 - metric2.duration / metric1.duration) * 100)}%`);
});

/**
 * Test 6: Database Index Performance
 */
const testDatabaseIndexes = test('Database Index Performance', async () => {
    const query = {
        userId: CONFIG.TEST_USER_ID,
        videoId: videoId,
        'doubts.0': { $exists: true }
    };

    const explain = await LessonMetricModel.collection.find(query).explain('executionStats');
    const stats = explain.executionStats;

    if (stats.winningPlan.inputStage.stage === 'COLLSCAN') {
        throw new Error('Query performed collection scan instead of using index');
    }

    const efficiency = stats.totalDocsReturned / stats.totalDocsExamined;
    if (efficiency < 0.5) {
        throw new Error(`Low query efficiency: ${(efficiency * 100).toFixed(1)}%`);
    }

    console.log(`   Index used: ${stats.winningPlan.inputStage.indexName || 'Unknown'}`);
    console.log(`   Query efficiency: ${(efficiency * 100).toFixed(1)}% (${stats.totalDocsReturned}/${stats.totalDocsExamined})`);
    console.log(`   Execution time: ${stats.executionTimeMillis}ms`);
});

/**
 * Test 7: End-to-End Performance
 */
const testEndToEndPerformance = test('End-to-End Performance', async () => {
    const timerId = performanceMonitor.startTimer('e2e_teacher_panel_load');
    
    const response = await fetch(`${CONFIG.CDS_API_URL}/user/comments/group/${groupId}?isDoubt=true&limit=20`, {
        headers: {
            'Authorization': token
        }
    });

    if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
    }

    const result = await response.json();
    const metric = performanceMonitor.endTimer(timerId);

    if (metric.duration > 5000) {
        throw new Error(`Teacher panel load too slow: ${metric.duration}ms`);
    }

    console.log(`   Teacher panel loaded in: ${metric.duration}ms`);
    console.log(`   Comments retrieved: ${result.comments?.length || 0}`);
    console.log(`   Performance: ${metric.duration < 1000 ? 'Excellent' : metric.duration < 3000 ? 'Good' : 'Acceptable'}`);
});

/**
 * Main execution function
 */
async function main() {
    try {
        console.log('🚀 Starting End-to-End Validation');
        console.log(`📊 Test Configuration:`);
        console.log(`   SAPS API: ${CONFIG.SAPS_API_URL}`);
        console.log(`   CDS API: ${CONFIG.CDS_API_URL}`);
        console.log(`   Video ID: ${videoId}`);
        console.log(`   Group ID: ${groupId}`);
        console.log(`   Has Token: ${!!token}`);

        await connectToDatabase();

        // Run all tests
        await testCommentCreation();
        await testCommentInfoRetrieval();
        await testCdsSync();
        await testTeacherPanelRetrieval();
        await testCachePerformance();
        await testDatabaseIndexes();
        await testEndToEndPerformance();

        // Display results
        console.log('\n' + '='.repeat(60));
        console.log('📊 END-TO-END VALIDATION RESULTS');
        console.log('='.repeat(60));
        console.log(`✅ Tests Passed: ${testResults.passed}`);
        console.log(`❌ Tests Failed: ${testResults.failed}`);
        console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);

        if (testResults.failed > 0) {
            console.log('\n❌ Failed Tests:');
            testResults.tests.filter(t => t.status === 'FAILED').forEach(test => {
                console.log(`   - ${test.name}: ${test.error}`);
            });
        }

        // Performance report
        const perfReport = performanceMonitor.getPerformanceReport();
        console.log('\n📈 Performance Summary:');
        Object.entries(perfReport.operations).forEach(([op, summary]) => {
            if (summary) {
                console.log(`   ${op}: avg ${Math.round(summary.averageTime)}ms (${summary.count} calls)`);
            }
        });

        console.log('\n✅ End-to-End validation completed!');
        process.exit(testResults.failed > 0 ? 1 : 0);

    } catch (error) {
        console.error('\n❌ End-to-End validation failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
    }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { testResults, CONFIG };
