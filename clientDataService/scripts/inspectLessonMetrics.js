#!/usr/bin/env node

/**
 * LessonMetrics Inspection Script
 * 
 * This script provides insights into the current state of LessonMetrics records:
 * 1. Shows sample records with their structure
 * 2. Analyzes videoId distribution
 * 3. Checks comment and doubt arrays
 * 4. Provides statistics about the collection
 * 
 * Usage:
 *   node scripts/inspectLessonMetrics.js [--limit=N]
 */

import mongoose from 'mongoose';
import { LessonMetricModel } from '../src/domain/models/metrics/lessonMetricsModel.js';

// Command line arguments
const args = process.argv.slice(2);
const limitArg = args.find(arg => arg.startsWith('--limit='));
const limit = limitArg ? parseInt(limitArg.split('=')[1]) : 10;

/**
 * Connect to MongoDB
 */
async function connectToDatabase() {
    try {
        const mongoUri = process.env.CONNECTION_STRING || process.env.MONGODB_URI || 'mongodb://localhost:27017/atomize-dev';
        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        console.log(`📍 Using database: ${mongoose.connection.name}`);
    } catch (error) {
        console.error('❌ Failed to connect to MongoDB:', error.message);
        process.exit(1);
    }
}

/**
 * Get collection statistics
 */
async function getCollectionStats() {
    try {
        const totalRecords = await LessonMetricModel.countDocuments();
        const recordsWithComments = await LessonMetricModel.countDocuments({ 
            comments: { $exists: true, $not: { $size: 0 } } 
        });
        const recordsWithDoubts = await LessonMetricModel.countDocuments({ 
            doubts: { $exists: true, $not: { $size: 0 } } 
        });
        const recordsWithNullVideoId = await LessonMetricModel.countDocuments({ 
            $or: [{ videoId: null }, { videoId: { $exists: false } }] 
        });
        
        // Get unique videoIds
        const uniqueVideoIds = await LessonMetricModel.distinct('videoId');
        
        // Get videoId distribution
        const videoIdDistribution = await LessonMetricModel.aggregate([
            { $group: { _id: '$videoId', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 10 }
        ]);
        
        return {
            totalRecords,
            recordsWithComments,
            recordsWithDoubts,
            recordsWithNullVideoId,
            uniqueVideoIds: uniqueVideoIds.length,
            videoIdDistribution
        };
    } catch (error) {
        console.error('❌ Error getting collection stats:', error.message);
        return null;
    }
}

/**
 * Get sample records
 */
async function getSampleRecords(limit) {
    try {
        const samples = await LessonMetricModel
            .find({})
            .limit(limit)
            .lean();
        
        return samples;
    } catch (error) {
        console.error('❌ Error getting sample records:', error.message);
        return [];
    }
}

/**
 * Analyze comment and doubt arrays
 */
async function analyzeCommentArrays() {
    try {
        // Get records with comments
        const recordsWithComments = await LessonMetricModel
            .find({ comments: { $exists: true, $not: { $size: 0 } } })
            .limit(5)
            .lean();
        
        // Get records with doubts
        const recordsWithDoubts = await LessonMetricModel
            .find({ doubts: { $exists: true, $not: { $size: 0 } } })
            .limit(5)
            .lean();
        
        // Analyze comment ID patterns
        const allCommentIds = [];
        recordsWithComments.forEach(record => {
            if (record.comments) {
                allCommentIds.push(...record.comments);
            }
        });
        
        // Analyze doubt ID patterns
        const allDoubtIds = [];
        recordsWithDoubts.forEach(record => {
            if (record.doubts) {
                record.doubts.forEach(doubt => {
                    if (doubt.commentId) {
                        allDoubtIds.push(doubt.commentId);
                    }
                });
            }
        });
        
        return {
            recordsWithComments,
            recordsWithDoubts,
            commentIdSamples: allCommentIds.slice(0, 10),
            doubtIdSamples: allDoubtIds.slice(0, 10),
            totalCommentIds: allCommentIds.length,
            totalDoubtIds: allDoubtIds.length
        };
    } catch (error) {
        console.error('❌ Error analyzing comment arrays:', error.message);
        return null;
    }
}

/**
 * Display results
 */
function displayResults(stats, samples, commentAnalysis) {
    console.log('\n' + '='.repeat(60));
    console.log('📊 LESSONMETRICS COLLECTION INSPECTION');
    console.log('='.repeat(60));
    
    if (stats) {
        console.log('\n📈 COLLECTION STATISTICS:');
        console.log(`   Total records: ${stats.totalRecords}`);
        console.log(`   Records with comments: ${stats.recordsWithComments}`);
        console.log(`   Records with doubts: ${stats.recordsWithDoubts}`);
        console.log(`   Records with null videoId: ${stats.recordsWithNullVideoId}`);
        console.log(`   Unique videoIds: ${stats.uniqueVideoIds}`);
        
        console.log('\n🎯 TOP VIDEO IDS BY RECORD COUNT:');
        stats.videoIdDistribution.forEach((item, index) => {
            console.log(`   ${index + 1}. VideoId ${item._id}: ${item.count} records`);
        });
    }
    
    if (samples && samples.length > 0) {
        console.log(`\n📋 SAMPLE RECORDS (showing ${samples.length}):`);
        samples.forEach((record, index) => {
            console.log(`\n   Record ${index + 1}:`);
            console.log(`     ID: ${record._id}`);
            console.log(`     VideoId: ${record.videoId}`);
            console.log(`     PlaylistId: ${record.playlistId || 'null'}`);
            console.log(`     UserId: ${record.userId}`);
            console.log(`     Comments: ${record.comments ? record.comments.length : 0} items`);
            console.log(`     Doubts: ${record.doubts ? record.doubts.length : 0} items`);
            console.log(`     Created: ${record.createdAt}`);
            console.log(`     Updated: ${record.updatedAt}`);
            
            if (record.comments && record.comments.length > 0) {
                console.log(`     Comment IDs: ${record.comments.slice(0, 3).join(', ')}${record.comments.length > 3 ? '...' : ''}`);
            }
            
            if (record.doubts && record.doubts.length > 0) {
                const doubtIds = record.doubts.slice(0, 3).map(d => d.commentId).join(', ');
                console.log(`     Doubt IDs: ${doubtIds}${record.doubts.length > 3 ? '...' : ''}`);
            }
        });
    }
    
    if (commentAnalysis) {
        console.log('\n💬 COMMENT ANALYSIS:');
        console.log(`   Total comment IDs found: ${commentAnalysis.totalCommentIds}`);
        console.log(`   Total doubt IDs found: ${commentAnalysis.totalDoubtIds}`);
        
        if (commentAnalysis.commentIdSamples.length > 0) {
            console.log(`   Sample comment IDs: ${commentAnalysis.commentIdSamples.join(', ')}`);
        }
        
        if (commentAnalysis.doubtIdSamples.length > 0) {
            console.log(`   Sample doubt IDs: ${commentAnalysis.doubtIdSamples.join(', ')}`);
        }
    }
    
    console.log('\n' + '='.repeat(60));
}

/**
 * Main execution function
 */
async function main() {
    try {
        console.log(`🔍 Inspecting LessonMetrics collection (limit: ${limit})...`);
        
        await connectToDatabase();
        
        console.log('\n📊 Gathering statistics...');
        const stats = await getCollectionStats();
        
        console.log('📋 Fetching sample records...');
        const samples = await getSampleRecords(limit);
        
        console.log('💬 Analyzing comment arrays...');
        const commentAnalysis = await analyzeCommentArrays();
        
        displayResults(stats, samples, commentAnalysis);
        
        console.log('\n✅ LessonMetrics inspection completed successfully!');
        process.exit(0);
        
    } catch (error) {
        console.error('\n❌ LessonMetrics inspection failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
    }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { getCollectionStats, getSampleRecords, analyzeCommentArrays };
