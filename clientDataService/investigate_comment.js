#!/usr/bin/env node

// Script para verificar especificamente o comentário do Davi
import { UserModel } from './src/domain/models/userModel.js';
import { GroupModel } from './src/modules/groups/schema/groupSchema.js';
import { LessonMetricModel } from './src/domain/models/metrics/lessonMetricsModel.js';
import connectToDB from './src/infra/libs/mongodb/connect.js';

async function investigateComment() {
    try {
        await connectToDB();
        console.log('🔗 Conectado ao MongoDB CDS');

        const userId = '68b61502bf77c9e762f32da6';
        const commentId = 43;
        const videoId = 234;

        console.log(`\n=== INVESTIGANDO COMENTÁRIO ${commentId} DO USUÁRIO ${userId} ===`);

        // 1. Verificar informações do usuário
        const user = await UserModel.findById(userId);
        if (!user) {
            console.log('❌ Usuário não encontrado no CDS');
            return;
        }

        console.log('\n👤 INFORMAÇÕES DO USUÁRIO:');
        console.log(`Nome: ${user.name}`);
        console.log(`Email: ${user.email}`);
        console.log(`Role: ${user.role}`);
        console.log(`School ID: ${user.schoolId}`);
        console.log(`Contract ID: ${user.contractId}`);

        // 2. Verificar grupos do usuário
        const userGroups = await GroupModel.find({ members: userId });
        console.log(`\n👥 GRUPOS DO USUÁRIO (${userGroups.length}):`);
        
        userGroups.forEach((group, index) => {
            console.log(`${index + 1}. "${group.name}" (${group.category}) - ID: ${group._id}`);
            console.log(`   Membros: ${group.members.length}`);
            if (group.parentGroupId) {
                console.log(`   Grupo pai: ${group.parentGroupId}`);
            }
        });

        // 3. Verificar métricas do usuário para o vídeo específico
        console.log(`\n🎯 VERIFICANDO MÉTRICAS PARA O VÍDEO ${videoId}:`);
        
        const userMetrics = await LessonMetricModel.find({
            userId: userId,
            videoId: videoId
        });

        if (userMetrics.length === 0) {
            console.log('❌ NENHUMA métrica encontrada para este vídeo!');
            console.log('   Isso significa que o comentário NÃO foi sincronizado do SAPS para o CDS');
        } else {
            console.log(`✅ ${userMetrics.length} métrica(s) encontrada(s):`);
            
            userMetrics.forEach((metric, index) => {
                console.log(`\n📊 MÉTRICA ${index + 1}:`);
                console.log(`   ID: ${metric._id}`);
                console.log(`   Video ID: ${metric.videoId}`);
                console.log(`   Playlist ID: ${metric.playlistId}`);
                console.log(`   Comments: [${metric.comments?.join(', ') || 'nenhum'}]`);
                console.log(`   Doubts: ${JSON.stringify(metric.doubts || [], null, 2)}`);
                console.log(`   Created: ${metric.createdAt}`);
                console.log(`   Updated: ${metric.updatedAt}`);

                // Verificar se nosso comentário está lá
                const hasComment = metric.comments && metric.comments.includes(commentId);
                const hasDoubt = metric.doubts && metric.doubts.some(doubt => doubt.commentId === commentId);

                if (hasComment) {
                    console.log(`   ✅ COMENTÁRIO ${commentId} encontrado em comments`);
                }
                if (hasDoubt) {
                    console.log(`   ✅ COMENTÁRIO ${commentId} encontrado em doubts`);
                    const doubt = metric.doubts.find(d => d.commentId === commentId);
                    console.log(`   Status da dúvida: ${doubt.status} (-1=pendente, 0=em andamento, 1=resolvida)`);
                } else if (!hasComment) {
                    console.log(`   ❌ COMENTÁRIO ${commentId} NÃO encontrado nesta métrica`);
                }
            });
        }

        // 4. Verificar se existem outras métricas do usuário
        const allUserMetrics = await LessonMetricModel.find({ userId: userId });
        console.log(`\n📈 TOTAL DE MÉTRICAS DO USUÁRIO: ${allUserMetrics.length}`);
        
        // Procurar o comentário em todas as métricas
        const metricsWithComment = allUserMetrics.filter(metric => 
            (metric.comments && metric.comments.includes(commentId)) ||
            (metric.doubts && metric.doubts.some(doubt => doubt.commentId === commentId))
        );

        if (metricsWithComment.length > 0) {
            console.log(`\n🎯 COMENTÁRIO ${commentId} ENCONTRADO EM ${metricsWithComment.length} MÉTRICA(S):`);
            metricsWithComment.forEach((metric, index) => {
                console.log(`${index + 1}. Video: ${metric.videoId}, Playlist: ${metric.playlistId}`);
            });
        } else {
            console.log(`\n❌ COMENTÁRIO ${commentId} NÃO ENCONTRADO EM NENHUMA MÉTRICA!`);
            console.log('   🔧 POSSÍVEIS SOLUÇÕES:');
            console.log('   1. Verificar se o comentário foi salvo corretamente no SAPS');
            console.log('   2. Verificar se a sincronização SAPS→CDS está funcionando');
            console.log('   3. Verificar logs do endpoint CDS que recebe comentários');
        }

        // 5. Para cada grupo do usuário, simular a busca que o professor faria
        console.log(`\n🔍 SIMULANDO BUSCA DE DÚVIDAS COMO PROFESSOR:`);
        
        for (const group of userGroups) {
            console.log(`\n🏫 Grupo: "${group.name}" (${group._id})`);
            
            // Simular a query que o CDS faz para buscar dúvidas
            const doubtsInGroup = await LessonMetricModel.find({
                userId: { $in: group.members },
                doubts: { 
                    $elemMatch: { status: -1 } // Dúvidas pendentes
                }
            });

            console.log(`   Métricas com dúvidas pendentes: ${doubtsInGroup.length}`);
            
            // Verificar se nosso comentário apareceria aqui
            const ourDoubtInGroup = doubtsInGroup.find(metric =>
                metric.doubts && metric.doubts.some(doubt => 
                    doubt.commentId === commentId && doubt.status === -1
                )
            );

            if (ourDoubtInGroup) {
                console.log(`   ✅ SUA DÚVIDA APARECERIA NESTE GRUPO!`);
            } else {
                console.log(`   ❌ Sua dúvida NÃO apareceria neste grupo`);
            }
        }

    } catch (error) {
        console.error('❌ Erro:', error);
    } finally {
        process.exit(0);
    }
}

investigateComment();