#!/usr/bin/env node

/**
 * Debug script to test the teacher panel doubt retrieval flow
 * Tests: Frontend -> SAPS -> CDS -> Database query -> Response enrichment
 */

import fetch from 'node-fetch';

// Configuration - Update these with actual values
const CONFIG = {
    SAPS_API_URL: 'https://your-saps-api-url.com',
    CDS_API_URL: 'https://your-cds-api-url.com',
    TEST_TEACHER_ID: 'test-teacher-id',
    TEST_GROUP_IDS: ['group-1', 'group-2'], // Teacher's groups
    AUTH_TOKEN: 'Bearer your-auth-token'
};

/**
 * Test 1: Fetch teacher groups
 */
async function testTeacherGroups() {
    console.log('👩‍🏫 Testing teacher groups retrieval...\n');
    
    try {
        // This would typically be a CDS call to get teacher groups
        console.log('📚 Fetching teacher groups from CDS...');
        
        // Mock response for now - replace with actual CDS call
        const mockGroups = CONFIG.TEST_GROUP_IDS.map(id => ({
            id,
            name: `Test Group ${id}`,
            members: ['student1', 'student2', 'student3']
        }));
        
        console.log('✅ Teacher Groups:', JSON.stringify(mockGroups, null, 2));
        return mockGroups;
        
    } catch (error) {
        console.error('❌ Error fetching teacher groups:', error.message);
        return [];
    }
}

/**
 * Test 2: Test individual group doubt retrieval
 */
async function testSingleGroupDoubts(groupId) {
    console.log(`\n🔍 Testing doubt retrieval for group: ${groupId}...\n`);
    
    try {
        console.log('📝 Calling SAPS getCommentsByGroup endpoint...');
        const response = await fetch(
            `${CONFIG.SAPS_API_URL}/video/comments/group/${groupId}?isDoubt=true&limit=10`,
            {
                headers: {
                    'Authorization': CONFIG.AUTH_TOKEN,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ SAPS Response:', JSON.stringify(result, null, 2));
        
        return result.comments || [];
        
    } catch (error) {
        console.error(`❌ Error fetching doubts for group ${groupId}:`, error.message);
        return [];
    }
}

/**
 * Test 3: Test CDS direct query (if accessible)
 */
async function testCDSDirectQuery(groupId) {
    console.log(`\n🗄️  Testing direct CDS query for group: ${groupId}...\n`);
    
    try {
        console.log('📊 Calling CDS getCommentsByGroupId endpoint...');
        const response = await fetch(
            `${CONFIG.CDS_API_URL}/user/comments/group/${groupId}?isDoubt=true&limit=10`,
            {
                headers: {
                    'Authorization': CONFIG.AUTH_TOKEN,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ CDS Response:', JSON.stringify(result, null, 2));
        
        return result.comments || [];
        
    } catch (error) {
        console.error(`❌ Error querying CDS for group ${groupId}:`, error.message);
        return [];
    }
}

/**
 * Test 4: Test multiple group aggregation (like frontend does)
 */
async function testMultipleGroupAggregation(groupIds) {
    console.log('\n🔄 Testing multiple group aggregation...\n');
    
    const allDoubts = [];
    const results = await Promise.all(
        groupIds.map(async (groupId) => {
            try {
                const doubts = await testSingleGroupDoubts(groupId);
                return doubts;
            } catch (error) {
                console.error(`Error for group ${groupId}:`, error.message);
                return [];
            }
        })
    );

    results.forEach(doubts => allDoubts.push(...doubts));
    
    // Sort by creation date (newest first)
    const sortedDoubts = allDoubts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    console.log(`📊 Aggregated ${sortedDoubts.length} doubts from ${groupIds.length} groups`);
    
    if (sortedDoubts.length > 0) {
        console.log('📝 Sample doubts:');
        sortedDoubts.slice(0, 3).forEach((doubt, index) => {
            console.log(`   ${index + 1}. ${doubt.content} (Status: ${doubt.doubtStatus})`);
        });
    } else {
        console.log('⚠️  No doubts found across all groups');
    }
    
    return sortedDoubts;
}

/**
 * Test 5: Verify data enrichment process
 */
async function testDataEnrichment() {
    console.log('\n🔗 Testing data enrichment process...\n');
    
    console.log('📋 Data enrichment flow:');
    console.log('   1. CDS returns basic comment metadata (commentId, userId, doubtStatus)');
    console.log('   2. SAPS enriches with comment content and video details');
    console.log('   3. Final response includes complete comment information');
    
    console.log('\n⚠️  Manual verification needed:');
    console.log('   - Check if SAPS getCommentInfo endpoint is working');
    console.log('   - Verify comment content is being fetched from SAPS database');
    console.log('   - Ensure video information is properly attached');
}

/**
 * Test 6: Performance analysis
 */
async function testPerformanceAnalysis(groupIds) {
    console.log('\n⚡ Testing performance characteristics...\n');
    
    const startTime = Date.now();
    
    console.log(`📊 Making ${groupIds.length} parallel requests...`);
    const results = await Promise.all(
        groupIds.map(async (groupId) => {
            const groupStartTime = Date.now();
            const doubts = await testSingleGroupDoubts(groupId);
            const groupEndTime = Date.now();
            
            return {
                groupId,
                doubtsCount: doubts.length,
                responseTime: groupEndTime - groupStartTime
            };
        })
    );
    
    const totalTime = Date.now() - startTime;
    
    console.log('📈 Performance Results:');
    console.log(`   Total time: ${totalTime}ms`);
    console.log(`   Average per group: ${Math.round(totalTime / groupIds.length)}ms`);
    
    results.forEach(result => {
        console.log(`   Group ${result.groupId}: ${result.responseTime}ms (${result.doubtsCount} doubts)`);
    });
    
    if (totalTime > 5000) {
        console.log('⚠️  Performance issue detected: Total time > 5 seconds');
        console.log('   Consider implementing batch API endpoint');
    }
}

/**
 * Main debug flow
 */
async function runTeacherPanelDebug() {
    console.log('🚀 Starting Teacher Panel Debug Flow\n');
    console.log('=' .repeat(60));
    
    // Step 1: Test teacher groups
    const groups = await testTeacherGroups();
    const groupIds = groups.map(g => g.id);
    
    if (groupIds.length === 0) {
        console.log('❌ No groups found, cannot continue');
        return;
    }
    
    // Step 2: Test single group retrieval
    if (groupIds.length > 0) {
        await testSingleGroupDoubts(groupIds[0]);
    }
    
    // Step 3: Test CDS direct query (if accessible)
    if (groupIds.length > 0) {
        await testCDSDirectQuery(groupIds[0]);
    }
    
    // Step 4: Test multiple group aggregation
    await testMultipleGroupAggregation(groupIds);
    
    // Step 5: Test data enrichment
    await testDataEnrichment();
    
    // Step 6: Performance analysis
    await testPerformanceAnalysis(groupIds);
    
    console.log('\n' + '=' .repeat(60));
    console.log('🏁 Teacher Panel Debug completed');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Update CONFIG values with your actual API URLs and tokens');
    console.log('2. Run this script: node debug_teacher_panel.js');
    console.log('3. Check for any failures in the request chain');
    console.log('4. Verify that doubts exist in the CDS database');
    console.log('5. Test the data enrichment process manually if needed');
}

// Run the debug flow if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTeacherPanelDebug().catch(console.error);
}

export { 
    testTeacherGroups, 
    testSingleGroupDoubts, 
    testCDSDirectQuery, 
    testMultipleGroupAggregation 
};
